<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">FAQ Management</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="index.php?controller=faq&action=add" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Add New FAQ
            </a>
        </div>
    </div>

    <?php if (isset($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Categories Overview -->
    <?php if (!empty($categories)): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Categories Overview</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($categories as $category): ?>
                                <div class="col-md-3 mb-2">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary me-2"><?= $category['count'] ?></span>
                                        <span class="text-capitalize"><?= htmlspecialchars($category['category']) ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">FAQ Items</h5>
        </div>
        <div class="card-body">
            <?php if (empty($faq_items)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No FAQ items found</h5>
                    <p class="text-muted">Create your first FAQ item to get started.</p>
                    <a href="index.php?controller=faq&action=add" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First FAQ
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="faq-table">
                        <thead>
                            <tr>
                                <th width="50">Order</th>
                                <th>Question (EN)</th>
                                <th>Question (RO)</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Last Updated</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="sortable-faq">
                            <?php foreach ($faq_items as $faq): ?>
                                <tr data-id="<?= $faq['id'] ?>">
                                    <td>
                                        <span class="badge bg-secondary"><?= $faq['order_number'] ?></span>
                                        <i class="fas fa-grip-vertical text-muted ms-2" style="cursor: move;" title="Drag to reorder"></i>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars(substr($faq['question_en'], 0, 80)) ?><?= strlen($faq['question_en']) > 80 ? '...' : '' ?></strong>
                                    </td>
                                    <td>
                                        <?= htmlspecialchars(substr($faq['question_ro'], 0, 80)) ?><?= strlen($faq['question_ro']) > 80 ? '...' : '' ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info text-capitalize"><?= htmlspecialchars($faq['category']) ?></span>
                                    </td>
                                    <td>
                                        <?php if ($faq['active']): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('M j, Y g:i A', strtotime($faq['updated_at'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="index.php?controller=faq&action=edit&id=<?= $faq['id'] ?>" 
                                               class="btn btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="index.php?controller=faq&action=delete&id=<?= $faq['id'] ?>" 
                                               class="btn btn-outline-danger" title="Delete"
                                               onclick="return confirm('Are you sure you want to delete this FAQ item?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="mt-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> FAQ Management
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-2"><strong>How it works:</strong></p>
                <ul class="mb-3">
                    <li>Create frequently asked questions with answers in both languages</li>
                    <li>Organize FAQ items by categories (e.g., general, certification, pricing)</li>
                    <li>Use drag & drop to reorder FAQ items within the list</li>
                    <li>Rich text editor supports formatting, links, and lists in answers</li>
                    <li>Inactive FAQ items won't be displayed on the website</li>
                </ul>
                
                <p class="mb-2"><strong>Category Examples:</strong></p>
                <ul class="mb-0">
                    <li><code>general</code> - General questions about the company</li>
                    <li><code>certification</code> - Questions about certification process</li>
                    <li><code>pricing</code> - Questions about pricing and costs</li>
                    <li><code>technical</code> - Technical questions about standards</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Include jQuery UI for sortable functionality -->
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

<script>
$(document).ready(function() {
    // Make FAQ table sortable
    $("#sortable-faq").sortable({
        handle: ".fa-grip-vertical",
        update: function(event, ui) {
            var order = $(this).sortable('toArray', {attribute: 'data-id'});
            
            // Send AJAX request to update order
            $.post('index.php?controller=faq&action=reorder', {
                order: order
            }, function(response) {
                if (response.success) {
                    // Update order numbers in the display
                    $("#sortable-faq tr").each(function(index) {
                        $(this).find('.badge').text(index + 1);
                    });
                } else {
                    alert('Error reordering FAQ items: ' + (response.error || 'Unknown error'));
                    // Reload page to reset order
                    location.reload();
                }
            }, 'json').fail(function() {
                alert('Error reordering FAQ items');
                location.reload();
            });
        }
    });
    
    // Add cursor pointer to sortable rows
    $("#sortable-faq tr").css('cursor', 'move');
});
</script>
