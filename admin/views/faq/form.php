<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2"><?= empty($faq['id']) ? 'Add New FAQ Item' : 'Edit FAQ Item' ?></h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="index.php?controller=faq&action=index" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to FAQ
            </a>
        </div>
    </div>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6>Please fix the following errors:</h6>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <form method="post" action="">
        <input type="hidden" name="id" value="<?= $faq['id'] ?>">
        
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">FAQ Content</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="question_en" class="form-label">Question (English) <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="question_en" name="question_en" rows="3" required 
                                      placeholder="Enter the question in English"><?= htmlspecialchars($faq['question_en']) ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="question_ro" class="form-label">Question (Romanian) <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="question_ro" name="question_ro" rows="3" required 
                                      placeholder="Enter the question in Romanian"><?= htmlspecialchars($faq['question_ro']) ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="answer_en" class="form-label">Answer (English) <span class="text-danger">*</span></label>
                            <textarea class="form-control summernote" id="answer_en" name="answer_en" rows="8" required><?= htmlspecialchars($faq['answer_en']) ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="answer_ro" class="form-label">Answer (Romanian) <span class="text-danger">*</span></label>
                            <textarea class="form-control summernote" id="answer_ro" name="answer_ro" rows="8" required><?= htmlspecialchars($faq['answer_ro']) ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="general" <?= $faq['category'] === 'general' ? 'selected' : '' ?>>General</option>
                                    <option value="certification" <?= $faq['category'] === 'certification' ? 'selected' : '' ?>>Certification</option>
                                    <option value="pricing" <?= $faq['category'] === 'pricing' ? 'selected' : '' ?>>Pricing</option>
                                    <option value="technical" <?= $faq['category'] === 'technical' ? 'selected' : '' ?>>Technical</option>
                                    <option value="support" <?= $faq['category'] === 'support' ? 'selected' : '' ?>>Support</option>
                                    <?php if (!empty($categories)): ?>
                                        <?php foreach ($categories as $cat): ?>
                                            <?php if (!in_array($cat, ['general', 'certification', 'pricing', 'technical', 'support'])): ?>
                                                <option value="<?= htmlspecialchars($cat) ?>" <?= $faq['category'] === $cat ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars(ucfirst($cat)) ?>
                                                </option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                    <option value="custom">+ Add New Category</option>
                                </select>
                                <button class="btn btn-outline-secondary" type="button" id="toggle-custom-category">
                                    <i class="fas fa-edit"></i>
                                </button>
                            </div>
                            <input type="text" class="form-control mt-2 d-none" id="custom-category" 
                                   placeholder="Enter new category name" value="<?= htmlspecialchars($faq['category']) ?>">
                            <div class="form-text">Group related FAQ items together</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="order_number" class="form-label">Order Number</label>
                            <input type="number" class="form-control" id="order_number" name="order_number" 
                                   value="<?= $faq['order_number'] ?>" min="0">
                            <div class="form-text">Controls display order (lower numbers appear first)</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="active" name="active" 
                                       <?= $faq['active'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="active">
                                    Active
                                </label>
                                <div class="form-text">Only active FAQ items are displayed on the website</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 
                                <?= empty($faq['id']) ? 'Add FAQ Item' : 'Update FAQ Item' ?>
                            </button>
                            <a href="index.php?controller=faq&action=index" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> Tips
                        </h6>
                    </div>
                    <div class="card-body">
                        <small>
                            <ul class="mb-0">
                                <li>Keep questions clear and concise</li>
                                <li>Use the rich text editor for formatting answers</li>
                                <li>Add links to relevant pages or documents</li>
                                <li>Group related questions using categories</li>
                                <li>Both English and Romanian content are required</li>
                            </ul>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const categorySelect = document.getElementById('category');
    const customCategoryInput = document.getElementById('custom-category');
    const toggleButton = document.getElementById('toggle-custom-category');
    
    // Handle category selection
    categorySelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customCategoryInput.classList.remove('d-none');
            customCategoryInput.focus();
            customCategoryInput.required = true;
        } else {
            customCategoryInput.classList.add('d-none');
            customCategoryInput.required = false;
        }
    });
    
    // Handle toggle button for custom category
    toggleButton.addEventListener('click', function() {
        if (customCategoryInput.classList.contains('d-none')) {
            categorySelect.value = 'custom';
            customCategoryInput.classList.remove('d-none');
            customCategoryInput.focus();
            customCategoryInput.required = true;
        } else {
            customCategoryInput.classList.add('d-none');
            customCategoryInput.required = false;
            categorySelect.value = '';
        }
    });
    
    // Handle form submission to use custom category value
    document.querySelector('form').addEventListener('submit', function() {
        if (categorySelect.value === 'custom' && customCategoryInput.value.trim()) {
            // Create a hidden input with the custom category value
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'category';
            hiddenInput.value = customCategoryInput.value.trim().toLowerCase();
            this.appendChild(hiddenInput);
            
            // Disable the select to prevent it from being submitted
            categorySelect.disabled = true;
        }
    });
    
    // Check if we need to show custom category input on page load
    if (categorySelect.value && !Array.from(categorySelect.options).some(option => option.value === categorySelect.value && option.value !== 'custom')) {
        customCategoryInput.value = categorySelect.value;
        categorySelect.value = 'custom';
        customCategoryInput.classList.remove('d-none');
        customCategoryInput.required = true;
    }
});
</script>
