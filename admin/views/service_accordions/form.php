<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2"><?= empty($accordion['id']) ? 'Add New Accordion Item' : 'Edit Accordion Item' ?></h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="index.php?controller=serviceAccordion&action=index&service_id=<?= $service['id'] ?>" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Accordions
            </a>
        </div>
    </div>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6>Please fix the following errors:</h6>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- Service Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Service: <?= htmlspecialchars($service['title_en']) ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>English Title:</strong> <?= htmlspecialchars($service['title_en']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Romanian Title:</strong> <?= htmlspecialchars($service['title_ro']) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <form method="post" action="">
        <input type="hidden" name="id" value="<?= $accordion['id'] ?>">
        <input type="hidden" name="service_id" value="<?= $service['id'] ?>">
        
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Accordion Content</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="title_en" class="form-label">Title (English) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title_en" name="title_en" 
                                   value="<?= htmlspecialchars($accordion['title_en']) ?>" required
                                   placeholder="Enter accordion title in English">
                        </div>
                        
                        <div class="mb-3">
                            <label for="title_ro" class="form-label">Title (Romanian) <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title_ro" name="title_ro" 
                                   value="<?= htmlspecialchars($accordion['title_ro']) ?>" required
                                   placeholder="Enter accordion title in Romanian">
                        </div>
                        
                        <div class="mb-3">
                            <label for="content_en" class="form-label">Content (English) <span class="text-danger">*</span></label>
                            <textarea class="form-control summernote" id="content_en" name="content_en" rows="10" required><?= htmlspecialchars($accordion['content_en']) ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="content_ro" class="form-label">Content (Romanian) <span class="text-danger">*</span></label>
                            <textarea class="form-control summernote" id="content_ro" name="content_ro" rows="10" required><?= htmlspecialchars($accordion['content_ro']) ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="order_number" class="form-label">Order Number</label>
                            <input type="number" class="form-control" id="order_number" name="order_number" 
                                   value="<?= $accordion['order_number'] ?>" min="0">
                            <div class="form-text">Controls display order (lower numbers appear first)</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="active" name="active" 
                                       <?= $accordion['active'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="active">
                                    Active
                                </label>
                                <div class="form-text">Only active accordion items are displayed on the website</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 
                                <?= empty($accordion['id']) ? 'Add Accordion Item' : 'Update Accordion Item' ?>
                            </button>
                            <a href="index.php?controller=serviceAccordion&action=index&service_id=<?= $service['id'] ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> Tips
                        </h6>
                    </div>
                    <div class="card-body">
                        <small>
                            <ul class="mb-0">
                                <li>Use clear, descriptive titles that explain the content</li>
                                <li>The WYSIWYG editor supports rich formatting, links, and lists</li>
                                <li>Order numbers help organize accordion display sequence</li>
                                <li>Both English and Romanian content are required</li>
                                <li>Consider organizing content logically (overview, process, benefits)</li>
                            </ul>
                        </small>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-lightbulb"></i> Content Ideas
                        </h6>
                    </div>
                    <div class="card-body">
                        <small>
                            <ul class="mb-0">
                                <li><strong>What is [Service]?</strong> - Overview and definition</li>
                                <li><strong>Benefits</strong> - Key advantages and value</li>
                                <li><strong>Process</strong> - Step-by-step procedure</li>
                                <li><strong>Requirements</strong> - What's needed to get started</li>
                                <li><strong>Timeline</strong> - How long the process takes</li>
                                <li><strong>Maintenance</strong> - Ongoing requirements</li>
                            </ul>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
