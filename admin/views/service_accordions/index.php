<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Service Accordions: <?= htmlspecialchars($service['title_en']) ?></h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="index.php?controller=services&action=index" class="btn btn-sm btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> Back to Services
            </a>
            <a href="index.php?controller=serviceAccordion&action=add&service_id=<?= $service['id'] ?>" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Add Accordion Item
            </a>
        </div>
    </div>

    <?php if (isset($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Service Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Service Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>English Title:</strong> <?= htmlspecialchars($service['title_en']) ?></p>
                            <p><strong>Romanian Title:</strong> <?= htmlspecialchars($service['title_ro']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Status:</strong> 
                                <?php if ($service['active']): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Inactive</span>
                                <?php endif; ?>
                            </p>
                            <p><strong>Accordion Items:</strong> <span class="badge bg-info"><?= count($accordions) ?></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Accordion Items -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Accordion Items</h5>
        </div>
        <div class="card-body">
            <?php if (empty($accordions)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No accordion items found</h5>
                    <p class="text-muted">Create your first accordion item for this service.</p>
                    <a href="index.php?controller=serviceAccordion&action=add&service_id=<?= $service['id'] ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Accordion Item
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="accordion-table">
                        <thead>
                            <tr>
                                <th width="50">Order</th>
                                <th>Title (EN)</th>
                                <th>Title (RO)</th>
                                <th>Status</th>
                                <th>Last Updated</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="sortable-accordions">
                            <?php foreach ($accordions as $accordion): ?>
                                <tr data-id="<?= $accordion['id'] ?>">
                                    <td>
                                        <span class="badge bg-secondary"><?= $accordion['order_number'] ?></span>
                                        <i class="fas fa-grip-vertical text-muted ms-2" style="cursor: move;" title="Drag to reorder"></i>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars(substr($accordion['title_en'], 0, 60)) ?><?= strlen($accordion['title_en']) > 60 ? '...' : '' ?></strong>
                                    </td>
                                    <td>
                                        <?= htmlspecialchars(substr($accordion['title_ro'], 0, 60)) ?><?= strlen($accordion['title_ro']) > 60 ? '...' : '' ?>
                                    </td>
                                    <td>
                                        <?php if ($accordion['active']): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('M j, Y g:i A', strtotime($accordion['updated_at'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="index.php?controller=serviceAccordion&action=edit&id=<?= $accordion['id'] ?>"
                                               class="btn btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="index.php?controller=serviceAccordion&action=delete&id=<?= $accordion['id'] ?>"
                                               class="btn btn-outline-danger" title="Delete"
                                               onclick="return confirm('Are you sure you want to delete this accordion item?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="mt-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Service Accordion Management
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-2"><strong>How it works:</strong></p>
                <ul class="mb-3">
                    <li>Create multiple accordion items for detailed service information</li>
                    <li>Each accordion item has a title and rich content in both languages</li>
                    <li>Use drag & drop to reorder accordion items</li>
                    <li>Rich text editor supports formatting, links, and lists</li>
                    <li>Inactive accordion items won't be displayed on the website</li>
                </ul>
                
                <p class="mb-2"><strong>Best Practices:</strong></p>
                <ul class="mb-0">
                    <li>Use clear, descriptive titles for each accordion item</li>
                    <li>Organize content logically (overview, process, benefits, etc.)</li>
                    <li>Keep content concise but informative</li>
                    <li>Include relevant links and formatting for better readability</li>
                </ul>
            </div>
        </div>
    </div>
</div>