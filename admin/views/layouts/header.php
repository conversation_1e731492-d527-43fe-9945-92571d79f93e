<!DOCTYPE html>
<html lang="<?= LANG ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?? 'RIGCERT Admin' ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Summernote WYSIWYG Editor -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php if (isset($_SESSION['user_id'])): ?>
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <img src="<?= ASSETS_URL ?>/img/logo/logo.png" alt="RIGCERT Logo" class="img-fluid" style="max-height: 60px;">
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= ($controller ?? '') === 'dashboard' ? 'active' : '' ?>" href="index.php?controller=dashboard&action=index">
                                <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= ($controller ?? '') === 'services' ? 'active' : '' ?>" href="index.php?controller=services&action=index">
                                <i class="fas fa-cogs me-2"></i> Services
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= ($controller ?? '') === 'news' ? 'active' : '' ?>" href="index.php?controller=news&action=index">
                                <i class="fas fa-newspaper me-2"></i> News
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= ($controller ?? '') === 'about' ? 'active' : '' ?>" href="index.php?controller=about&action=index">
                                <i class="fas fa-info-circle me-2"></i> About Content
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= ($controller ?? '') === 'faq' ? 'active' : '' ?>" href="index.php?controller=faq&action=index">
                                <i class="fas fa-question-circle me-2"></i> FAQ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= ($controller ?? '') === 'privacy' ? 'active' : '' ?>" href="index.php?controller=privacy&action=index">
                                <i class="fas fa-shield-alt me-2"></i> Privacy Policy
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= ($controller ?? '') === 'translations' ? 'active' : '' ?>" href="index.php?controller=translations&action=index">
                                <i class="fas fa-language me-2"></i> Translations
                            </a>
                        </li>
                        <?php if (isset($user) && $user['role'] === 'admin'): ?>
                        <li class="nav-item">
                            <a class="nav-link <?= ($controller ?? '') === 'users' ? 'active' : '' ?>" href="index.php?controller=users&action=index">
                                <i class="fas fa-users me-2"></i> Users
                            </a>
                        </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link <?= ($controller ?? '') === 'media' ? 'active' : '' ?>" href="index.php?controller=media&action=index">
                                <i class="fas fa-images me-2"></i> Media
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            <?php endif; ?>

            <!-- Main content -->
            <main class="<?= isset($_SESSION['user_id']) ? 'col-md-9 ms-sm-auto col-lg-10 px-md-4' : 'col-12' ?>">
                <?php if (isset($_SESSION['user_id'])): ?>
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?= $page_title ?? 'RIGCERT Admin' ?></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i> <?= $user['username'] ?? 'User' ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="index.php?controller=users&action=profile"><i class="fas fa-user-cog me-1"></i> Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-1"></i> Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= $success ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= $error ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
