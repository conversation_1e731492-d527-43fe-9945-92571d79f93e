            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Summernote WYSIWYG Editor -->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/admin.js"></script>

	<!-- Include jQuery UI for sortable functionality -->
	<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
	<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

	<script>
		$(document).ready(function() {
			// Make accordion table sortable
			$("#sortable-accordions").sortable({
				handle: ".fa-grip-vertical",
				update: function(event, ui) {
					var order = $(this).sortable('toArray', {attribute: 'data-id'});

					// Send AJAX request to update order
					$.post('index.php?controller=serviceAccordion&action=reorder', {
						order: order
					}, function(response) {
						if (response.success) {
							// Update order numbers in the display
							$("#sortable-accordions tr").each(function(index) {
								$(this).find('.badge').text(index + 1);
							});
						} else {
							alert('Error reordering accordion items: ' + (response.error || 'Unknown error'));
							// Reload page to reset order
							location.reload();
						}
					}, 'json').fail(function() {
						alert('Error reordering accordion items');
						location.reload();
					});
				}
			});

			// Add cursor pointer to sortable rows
			$("#sortable-accordions tr").css('cursor', 'move');
		});

        // Initialize Summernote WYSIWYG editor
        $(document).ready(function() {
            $('.summernote').summernote({
                height: 300,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ]
            });

            // File input preview
            $('.custom-file-input').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);

                // Preview image
                if (this.files && this.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#preview-image').attr('src', e.target.result);
                        $('#preview-container').show();
                    }
                    reader.readAsDataURL(this.files[0]);
                }
            });

            // Confirm delete
            $('.btn-delete').on('click', function(e) {
                if (!confirm('Are you sure you want to delete this item?')) {
                    e.preventDefault();
                }
            });

            // Generate slug from title
            $('#title-en').on('keyup', function() {
                var title = $(this).val();
                var slug = title.toLowerCase()
                    .replace(/[^\w ]+/g, '')
                    .replace(/ +/g, '-');
                $('#slug').val(slug);
            });
        });
    </script>
</body>
</html>
