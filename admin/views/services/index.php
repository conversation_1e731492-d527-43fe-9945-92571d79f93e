<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Services</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="index.php?controller=services&action=add" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Add New Service
            </a>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Title (EN)</th>
                    <th>Title (RO)</th>
                    <th>Image</th>
                    <th>Icon</th>
                    <th>Order</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($services)): ?>
                    <tr>
                        <td colspan="8" class="text-center">No services found</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($services as $service): ?>
                        <tr>
                            <td><?= $service['id'] ?></td>
                            <td><?= htmlspecialchars($service['title_en']) ?></td>
                            <td><?= htmlspecialchars($service['title_ro']) ?></td>
                            <td>
                                <?php if ($service['image']): ?>
                                    <img src="<?= ROOT_URL ?>/<?= $service['image'] ?>" alt="Image" class="preview-image">
                                <?php else: ?>
                                    <span class="text-muted">No image</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($service['icon']): ?>
                                    <img src="<?= ROOT_URL ?>/<?= $service['icon'] ?>" alt="Icon" class="preview-image">
                                <?php else: ?>
                                    <span class="text-muted">No icon</span>
                                <?php endif; ?>
                            </td>
                            <td><?= $service['order_number'] ?></td>
                            <td>
                                <?php if ($service['active']): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="index.php?controller=serviceAccordion&action=index&service_id=<?= $service['id'] ?>"
                                       class="btn btn-outline-info" title="Manage Accordions">
                                        <i class="fas fa-list"></i>
                                    </a>
                                    <a href="index.php?controller=services&action=edit&id=<?= $service['id'] ?>"
                                       class="btn btn-outline-primary" title="Edit Service">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="index.php?controller=services&action=delete&id=<?= $service['id'] ?>"
                                       class="btn btn-outline-danger btn-delete" title="Delete Service">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
