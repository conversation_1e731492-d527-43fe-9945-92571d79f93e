<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2"><?= empty($content['id']) ? 'Add New Privacy Policy Section' : 'Edit Privacy Policy Section' ?></h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="index.php?controller=privacy&action=index" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Privacy Policy
            </a>
        </div>
    </div>
    
    <?php if (isset($errors) && !empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6>Please fix the following errors:</h6>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <form method="post" action="">
        <input type="hidden" name="id" value="<?= $content['id'] ?>">
        
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Content Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="section" class="form-label">Section Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="section" name="section" 
                                           value="<?= htmlspecialchars($content['section']) ?>" required
                                           placeholder="e.g., introduction, data_collection">
                                    <div class="form-text">Unique identifier for this section (lowercase, underscores only)</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="order_number" class="form-label">Order Number</label>
                                    <input type="number" class="form-control" id="order_number" name="order_number" 
                                           value="<?= $content['order_number'] ?>" min="0">
                                    <div class="form-text">Controls display order (lower numbers appear first)</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title_en" class="form-label">Title (English)</label>
                                    <input type="text" class="form-control" id="title_en" name="title_en" 
                                           value="<?= htmlspecialchars($content['title_en']) ?>"
                                           placeholder="Section title in English">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title_ro" class="form-label">Title (Romanian)</label>
                                    <input type="text" class="form-control" id="title_ro" name="title_ro" 
                                           value="<?= htmlspecialchars($content['title_ro']) ?>"
                                           placeholder="Section title in Romanian">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="content_en" class="form-label">Content (English) <span class="text-danger">*</span></label>
                            <textarea class="form-control summernote" id="content_en" name="content_en" rows="10" required><?= htmlspecialchars($content['content_en']) ?></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="content_ro" class="form-label">Content (Romanian) <span class="text-danger">*</span></label>
                            <textarea class="form-control summernote" id="content_ro" name="content_ro" rows="10" required><?= htmlspecialchars($content['content_ro']) ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="active" name="active" 
                                       <?= $content['active'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="active">
                                    Active
                                </label>
                                <div class="form-text">Only active sections are displayed on the website</div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 
                                <?= empty($content['id']) ? 'Add Section' : 'Update Section' ?>
                            </button>
                            <a href="index.php?controller=privacy&action=index" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> Tips
                        </h6>
                    </div>
                    <div class="card-body">
                        <small>
                            <ul class="mb-0">
                                <li>Use descriptive section names like <code>introduction</code> or <code>data_collection</code></li>
                                <li>The WYSIWYG editor supports rich formatting, links, and lists</li>
                                <li>Order numbers help organize content display sequence</li>
                                <li>Both English and Romanian content are required</li>
                                <li>Include relevant legal information and contact details</li>
                            </ul>
                        </small>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-lightbulb"></i> Common Sections
                        </h6>
                    </div>
                    <div class="card-body">
                        <small>
                            <ul class="mb-0">
                                <li><strong>Introduction</strong> - Overview of privacy policy</li>
                                <li><strong>Data Collection</strong> - What information is collected</li>
                                <li><strong>Data Usage</strong> - How data is used</li>
                                <li><strong>Data Sharing</strong> - When data is shared</li>
                                <li><strong>Cookies</strong> - Cookie usage policy</li>
                                <li><strong>User Rights</strong> - User data rights</li>
                                <li><strong>Contact</strong> - Privacy contact information</li>
                            </ul>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate section name from title
    const titleEn = document.getElementById('title_en');
    const sectionInput = document.getElementById('section');
    
    if (titleEn && sectionInput && !sectionInput.value) {
        titleEn.addEventListener('input', function() {
            const title = this.value.toLowerCase()
                .replace(/[^a-z0-9\s]/g, '')
                .replace(/\s+/g, '_')
                .replace(/^_+|_+$/g, '');
            sectionInput.value = title;
        });
    }
});
</script>
