<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Privacy Policy Management</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="index.php?controller=privacy&action=add" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Add New Section
            </a>
        </div>
    </div>

    <?php if (isset($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($success) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Privacy Policy Sections</h5>
        </div>
        <div class="card-body">
            <?php if (empty($sections)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No privacy policy sections found</h5>
                    <p class="text-muted">Create your first privacy policy section to get started.</p>
                    <a href="index.php?controller=privacy&action=add" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add First Section
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Order</th>
                                <th>Section</th>
                                <th>Title (EN)</th>
                                <th>Title (RO)</th>
                                <th>Status</th>
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sections as $section): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary"><?= $section['order_number'] ?></span>
                                    </td>
                                    <td>
                                        <strong><?= htmlspecialchars($section['section']) ?></strong>
                                    </td>
                                    <td>
                                        <?= htmlspecialchars($section['title_en'] ?: 'No title') ?>
                                    </td>
                                    <td>
                                        <?= htmlspecialchars($section['title_ro'] ?: 'No title') ?>
                                    </td>
                                    <td>
                                        <?php if ($section['active']): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('M j, Y g:i A', strtotime($section['updated_at'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="index.php?controller=privacy&action=edit&id=<?= $section['id'] ?>" 
                                               class="btn btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="index.php?controller=privacy&action=delete&id=<?= $section['id'] ?>" 
                                               class="btn btn-outline-danger" title="Delete"
                                               onclick="return confirm('Are you sure you want to delete this section?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="mt-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Privacy Policy Management
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-2"><strong>How it works:</strong></p>
                <ul class="mb-3">
                    <li>Create different sections for your Privacy Policy page content</li>
                    <li>Each section has a unique identifier and can contain rich text content</li>
                    <li>Content is managed in both English and Romanian</li>
                    <li>Use the order number to control the display sequence</li>
                    <li>Inactive sections won't be displayed on the website</li>
                </ul>
                
                <p class="mb-2"><strong>Section Examples:</strong></p>
                <ul class="mb-0">
                    <li><code>introduction</code> - Privacy policy introduction</li>
                    <li><code>data_collection</code> - What data we collect</li>
                    <li><code>data_usage</code> - How we use your data</li>
                    <li><code>data_sharing</code> - Data sharing policies</li>
                    <li><code>cookies</code> - Cookie policy</li>
                    <li><code>contact</code> - Contact information for privacy concerns</li>
                </ul>
            </div>
        </div>
    </div>
</div>
