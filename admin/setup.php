<?php
// Setup script to initialize the database
session_start();
require_once __DIR__ . '/../config/config.php';

// Set controller for header
$controller = 'setup';
$action = 'index';

// Check if setup has already been completed
$setupCompleted = false;
try {
    $stmt = query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        $stmt = query("SELECT COUNT(*) FROM users");
        if ($stmt->fetchColumn() > 0) {
            $setupCompleted = true;
        }
    }
} catch (Exception $e) {
    // Table doesn't exist, setup not completed
}

// Handle form submission
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = $_POST['username'] ?? 'admin';
    $password = $_POST['password'] ?? 'admin123';
    $email = $_POST['email'] ?? '<EMAIL>';

    // Validate form data
    if (empty($username) || empty($password) || empty($email)) {
        $error = 'Please fill in all fields';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address';
    } else {
        try {
            // Read SQL file
            $sql = file_get_contents(__DIR__ . '/db_schema.sql');

            // Execute SQL statements
            $pdo->exec($sql);

            // Check if admin user exists
            $stmt = query("SELECT COUNT(*) FROM users WHERE username = :username", [
                'username' => $username
            ]);

            if ($stmt->fetchColumn() === 0) {
                // Create admin user
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                query("INSERT INTO users (username, password, email, role) VALUES (:username, :password, :email, 'admin')", [
                    'username' => $username,
                    'password' => $hashedPassword,
                    'email' => $email
                ]);
            }

            $success = 'Setup completed successfully. You can now <a href="login.php">login</a> to the admin dashboard.';
            $setupCompleted = true;
        } catch (Exception $e) {
            $error = 'Error setting up database: ' . $e->getMessage();
        }
    }
}

// Page title
$page_title = 'Setup | RIGCERT Admin';
?>
<!DOCTYPE html>
<html lang="<?= LANG ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/admin.css">

    <style>
        body {
            height: 100vh;
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
        }

        .form-setup {
            width: 100%;
            max-width: 500px;
            padding: 15px;
            margin: auto;
        }
    </style>
</head>
<body>
    <main class="form-setup">
        <div class="card shadow">
            <div class="card-header">
                <div class="text-center mb-2">
                    <img src="<?= ASSETS_URL ?>/img/logo/logo.png" alt="RIGCERT Logo" class="img-fluid" style="max-height: 80px;">
                    <h1 class="h3 mb-0 fw-normal">RIGCERT Admin Setup</h1>
                </div>
            </div>
            <div class="card-body">
                <?php if ($setupCompleted && $success): ?>
                    <div class="alert alert-success"><?= $success ?></div>
                    <div class="text-center">
                        <a href="login.php" class="btn btn-primary">Go to Login</a>
                    </div>
                <?php elseif ($setupCompleted): ?>
                    <div class="alert alert-info">Setup has already been completed. You can <a href="login.php">login</a> to the admin dashboard.</div>
                    <div class="text-center">
                        <a href="login.php" class="btn btn-primary">Go to Login</a>
                    </div>
                <?php else: ?>
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?= $error ?></div>
                    <?php endif; ?>

                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="username" class="form-label">Admin Username</label>
                            <input type="text" class="form-control" id="username" name="username" value="admin" required>
                            <div class="form-text">Default: admin</div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Admin Password</label>
                            <input type="password" class="form-control" id="password" name="password" value="admin123" required>
                            <div class="form-text">Default: admin123</div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Admin Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                            <div class="form-text">Default: <EMAIL></div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> This script will create the necessary database tables and a default admin user. Make sure to change the default password after login.
                        </div>

                        <button class="w-100 btn btn-lg btn-primary" type="submit">Run Setup</button>
                    </form>
                <?php endif; ?>
            </div>
            <div class="card-footer text-center text-muted">
                &copy; <?= date('Y') ?> RIGCERT
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
