<?php
require_once __DIR__ . '/base_controller.php';
require_once __DIR__ . '/../models/service_accordion_model.php';
require_once __DIR__ . '/../models/services_model.php';

/**
 * Service Accordion Controller
 * Handles service accordion related actions
 */
class ServiceAccordionController extends BaseController {
    private $serviceModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct();
        $this->model = new ServiceAccordionModel();
        $this->serviceModel = new ServiceModel();
        $this->data['page_title'] = 'Service Accordions | RIGCERT Admin';
    }
    
    /**
     * Index action - display accordions for a specific service
     */
    public function index() {
        $serviceId = $_GET['service_id'] ?? 0;
        
        // Get service information
        $service = $this->serviceModel->getServiceById($serviceId);
        if (!$service) {
            $this->setFlash('error', 'Service not found');
            $this->redirect('index.php?controller=services&action=index');
        }
        
        // Get accordions for this service
        $this->data['accordions'] = $this->model->getAccordionsByService($serviceId);
        $this->data['service'] = $service;
        
        // Render accordion list view
        $this->render('service_accordions/index', $this->data);
    }
    
    /**
     * Add action - add new accordion item
     */
    public function add() {
        $serviceId = $_GET['service_id'] ?? 0;
        
        // Get service information
        $service = $this->serviceModel->getServiceById($serviceId);
        if (!$service) {
            $this->setFlash('error', 'Service not found');
            $this->redirect('index.php?controller=services&action=index');
        }
        
        // Set page title
        $this->data['page_title'] = 'Add Accordion Item | RIGCERT Admin';
        
        // Initialize accordion data
        $this->data['accordion'] = [
            'id' => '',
            'service_id' => $serviceId,
            'title_en' => '',
            'title_ro' => '',
            'content_en' => '',
            'content_ro' => '',
            'order_number' => $this->model->getNextOrderNumber($serviceId),
            'active' => 1
        ];
        
        $this->data['service'] = $service;
        
        // Handle form submission
        if ($this->isPost()) {
            $this->processAccordionForm();
        }
        
        // Render accordion form view
        $this->render('service_accordions/form', $this->data);
    }
    
    /**
     * Edit action - edit existing accordion item
     */
    public function edit() {
        // Get accordion ID
        $id = $_GET['id'] ?? 0;
        
        // Get accordion data
        $accordion = $this->model->getAccordionById($id);
        
        if (!$accordion) {
            $this->setFlash('error', 'Accordion item not found');
            $this->redirect('index.php?controller=services&action=index');
        }
        
        // Get service information
        $service = $this->serviceModel->getServiceById($accordion['service_id']);
        if (!$service) {
            $this->setFlash('error', 'Service not found');
            $this->redirect('index.php?controller=services&action=index');
        }
        
        // Set page title
        $this->data['page_title'] = 'Edit Accordion Item | RIGCERT Admin';
        
        // Set accordion and service data
        $this->data['accordion'] = $accordion;
        $this->data['service'] = $service;
        
        // Handle form submission
        if ($this->isPost()) {
            $this->processAccordionForm();
        }
        
        // Render accordion form view
        $this->render('service_accordions/form', $this->data);
    }
    
    /**
     * Delete action - delete accordion item
     */
    public function delete() {
        // Get accordion ID
        $id = $_GET['id'] ?? 0;
        
        // Get accordion to find service ID for redirect
        $accordion = $this->model->getAccordionById($id);
        $serviceId = $accordion['service_id'] ?? 0;
        
        // Delete accordion
        if ($this->model->deleteAccordion($id)) {
            $this->setFlash('success', 'Accordion item deleted successfully');
        } else {
            $this->setFlash('error', 'Error deleting accordion item');
        }
        
        // Redirect to accordion list
        $this->redirect('index.php?controller=service_accordion&action=index&service_id=' . $serviceId);
    }
    
    /**
     * Reorder action - handle accordion reordering via AJAX
     */
    public function reorder() {
        if ($this->isPost() && isset($_POST['order'])) {
            $orderData = [];
            foreach ($_POST['order'] as $position => $id) {
                $orderData[$id] = $position + 1;
            }
            
            if ($this->model->reorderAccordions($orderData)) {
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Error reordering accordion items']);
            }
        } else {
            echo json_encode(['success' => false, 'error' => 'Invalid request']);
        }
        exit;
    }
    
    /**
     * Process accordion form submission
     */
    private function processAccordionForm() {
        // Get form data
        $accordion = [
            'id' => $_POST['id'] ?? '',
            'service_id' => intval($_POST['service_id'] ?? 0),
            'title_en' => trim($_POST['title_en'] ?? ''),
            'title_ro' => trim($_POST['title_ro'] ?? ''),
            'content_en' => $_POST['content_en'] ?? '',
            'content_ro' => $_POST['content_ro'] ?? '',
            'order_number' => intval($_POST['order_number'] ?? 0),
            'active' => isset($_POST['active']) ? 1 : 0
        ];
        
        // Validate required fields
        $errors = [];
        
        if (empty($accordion['service_id'])) {
            $errors[] = 'Service ID is required';
        }
        
        if (empty($accordion['title_en'])) {
            $errors[] = 'English title is required';
        }
        
        if (empty($accordion['title_ro'])) {
            $errors[] = 'Romanian title is required';
        }
        
        if (empty($accordion['content_en'])) {
            $errors[] = 'English content is required';
        }
        
        if (empty($accordion['content_ro'])) {
            $errors[] = 'Romanian content is required';
        }
        
        // If no errors, save accordion
        if (empty($errors)) {
            if (empty($accordion['id'])) {
                // Add new accordion
                if ($this->model->addAccordion($accordion)) {
                    $this->setFlash('success', 'Accordion item added successfully');
                    $this->redirect('index.php?controller=service_accordion&action=index&service_id=' . $accordion['service_id']);
                } else {
                    $errors[] = 'Error adding accordion item';
                }
            } else {
                // Update existing accordion
                if ($this->model->updateAccordion($accordion)) {
                    $this->setFlash('success', 'Accordion item updated successfully');
                    $this->redirect('index.php?controller=service_accordion&action=index&service_id=' . $accordion['service_id']);
                } else {
                    $errors[] = 'Error updating accordion item';
                }
            }
        }
        
        // Set errors and accordion data for form redisplay
        $this->data['errors'] = $errors;
        $this->data['accordion'] = $accordion;
    }
}
