<?php
require_once __DIR__ . '/base_controller.php';
require_once __DIR__ . '/../models/about_model.php';

/**
 * About Content Controller
 * Handles about content related actions
 */
class AboutController extends BaseController {
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct();
        $this->model = new AboutModel();
        $this->data['page_title'] = 'About Content | RIGCERT Admin';
    }
    
    /**
     * Index action - display about content sections list
     */
    public function index() {
        // Get all about content sections
        $this->data['sections'] = $this->model->getAllSections();
        
        // Render about content list view
        $this->render('about/index', $this->data);
    }
    
    /**
     * Add action - add new about content section
     */
    public function add() {
        // Set page title
        $this->data['page_title'] = 'Add About Content Section | RIGCERT Admin';
        
        // Initialize content data
        $this->data['content'] = [
            'id' => '',
            'section' => '',
            'title_en' => '',
            'title_ro' => '',
            'content_en' => '',
            'content_ro' => '',
            'image' => '',
            'order_number' => $this->getNextOrderNumber(),
            'active' => 1
        ];
        
        // Handle form submission
        if ($this->isPost()) {
            $this->processContentForm();
        }
        
        // Render about content form view
        $this->render('about/form', $this->data);
    }
    
    /**
     * Edit action - edit existing about content section
     */
    public function edit() {
        // Set page title
        $this->data['page_title'] = 'Edit About Content Section | RIGCERT Admin';
        
        // Get content ID
        $id = $_GET['id'] ?? 0;
        
        // Get content data
        $content = $this->model->getContentById($id);
        
        if (!$content) {
            // Content not found, redirect to content list
            $this->setFlash('error', 'About content section not found');
            $this->redirect('index.php?controller=about&action=index');
        }
        
        // Set content data
        $this->data['content'] = $content;
        
        // Handle form submission
        if ($this->isPost()) {
            $this->processContentForm();
        }
        
        // Render about content form view
        $this->render('about/form', $this->data);
    }
    
    /**
     * Delete action - delete about content section
     */
    public function delete() {
        // Get content ID
        $id = $_GET['id'] ?? 0;
        
        // Delete content
        if ($this->model->deleteContent($id)) {
            $this->setFlash('success', 'About content section deleted successfully');
        } else {
            $this->setFlash('error', 'Error deleting about content section');
        }
        
        // Redirect to content list
        $this->redirect('index.php?controller=about&action=index');
    }
    
    /**
     * Process content form submission
     */
    private function processContentForm() {
        // Get form data
        $content = [
            'id' => $_POST['id'] ?? '',
            'section' => trim($_POST['section'] ?? ''),
            'title_en' => trim($_POST['title_en'] ?? ''),
            'title_ro' => trim($_POST['title_ro'] ?? ''),
            'content_en' => $_POST['content_en'] ?? '',
            'content_ro' => $_POST['content_ro'] ?? '',
            'image' => $_POST['image'] ?? '',
            'order_number' => intval($_POST['order_number'] ?? 0),
            'active' => isset($_POST['active']) ? 1 : 0
        ];
        
        // Validate required fields
        $errors = [];
        
        if (empty($content['section'])) {
            $errors[] = 'Section name is required';
        }
        
        if (empty($content['content_en'])) {
            $errors[] = 'English content is required';
        }
        
        if (empty($content['content_ro'])) {
            $errors[] = 'Romanian content is required';
        }
        
        // Check if section name already exists (for new content or different content)
        if ($this->model->sectionExists($content['section'], $content['id'])) {
            $errors[] = 'Section name already exists';
        }
        
        // If no errors, save content
        if (empty($errors)) {
            // Handle file upload if present
            if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
                $uploadResult = $this->handleImageUpload($_FILES['image_file']);
                if ($uploadResult['success']) {
                    $content['image'] = $uploadResult['filename'];
                } else {
                    $errors[] = $uploadResult['error'];
                }
            }
            
            if (empty($errors)) {
                if (empty($content['id'])) {
                    // Add new content
                    if ($this->model->addContent($content)) {
                        $this->setFlash('success', 'About content section added successfully');
                        $this->redirect('index.php?controller=about&action=index');
                    } else {
                        $errors[] = 'Error adding about content section';
                    }
                } else {
                    // Update existing content
                    if ($this->model->updateContent($content)) {
                        $this->setFlash('success', 'About content section updated successfully');
                        $this->redirect('index.php?controller=about&action=index');
                    } else {
                        $errors[] = 'Error updating about content section';
                    }
                }
            }
        }
        
        // Set errors and content data for form redisplay
        $this->data['errors'] = $errors;
        $this->data['content'] = $content;
    }
    
    /**
     * Handle image upload
     */
    private function handleImageUpload($file) {
        $uploadDir = __DIR__ . '/../../uploads/about/';
        
        // Create upload directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            return ['success' => false, 'error' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'];
        }
        
        // Validate file size (max 5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            return ['success' => false, 'error' => 'File size too large. Maximum size is 5MB.'];
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'about_' . time() . '_' . uniqid() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return ['success' => true, 'filename' => 'uploads/about/' . $filename];
        } else {
            return ['success' => false, 'error' => 'Error uploading file.'];
        }
    }
    
    /**
     * Get next order number
     */
    private function getNextOrderNumber() {
        $maxOrder = getValue("SELECT MAX(order_number) FROM about_content");
        return ($maxOrder ?? 0) + 1;
    }
}
