<?php
/**
 * Base Controller
 * All other controllers will extend this class
 */
class BaseController {
    protected $model;
    protected $view;
    protected $data = [];

    /**
     * Constructor
     */
    public function __construct() {
        // Set default page title
        $this->data['page_title'] = 'RIGCERT Admin';

        // Set current user data
        if (isset($_SESSION['user_id'])) {
            $this->data['user'] = $this->getUserData($_SESSION['user_id']);
        }

        // Set current language
        $this->data['lang'] = LANG;

        // Set current controller and action
        $this->data['controller'] = $_GET['controller'] ?? 'dashboard';
        $this->data['action'] = $_GET['action'] ?? 'index';

        // Set flash messages
        $this->setFlashMessages();
    }

    /**
     * Get user data
     *
     * @param int $userId User ID
     * @return array User data
     */
    protected function getUserData($userId) {
        $stmt = query("SELECT id, username, email, role FROM users WHERE id = :id", [
            'id' => $userId
        ]);
        return $stmt->fetch();
    }

    /**
     * Set flash messages from session
     */
    protected function setFlashMessages() {
        // Set success message
        if (isset($_SESSION['success'])) {
            $this->data['success'] = $_SESSION['success'];
            unset($_SESSION['success']);
        }

        // Set error message
        if (isset($_SESSION['error'])) {
            $this->data['error'] = $_SESSION['error'];
            unset($_SESSION['error']);
        }
    }

    /**
     * Set flash message
     *
     * @param string $type Message type (success, error)
     * @param string $message Message text
     */
    protected function setFlash($type, $message) {
        $_SESSION[$type] = $message;
    }

    /**
     * Render view
     *
     * @param string $view View file name
     * @param array $data Data to pass to the view
     */
    protected function render($view, $data = []) {
        // Merge data
        $data = array_merge($this->data, $data);

        // Extract data to variables
        extract($data);

        // Include header
        require_once __DIR__ . '/../views/layouts/header.php';

        // Include view
        require_once __DIR__ . '/../views/' . $view . '.php';

        // Include footer
        require_once __DIR__ . '/../views/layouts/footer.php';
    }

    /**
     * Redirect to another page
     *
     * @param string $url URL to redirect to
     */
    protected function redirect($url) {
        header('Location: ' . $url);
        exit;
    }

    /**
     * Check if user has admin role
     *
     * @return bool True if user is admin
     */
    protected function isAdmin() {
        return isset($this->data['user']) && $this->data['user']['role'] === 'admin';
    }

    /**
     * Check if request is POST
     *
     * @return bool True if request is POST
     */
    protected function isPost() {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }
}
