<?php
require_once __DIR__ . '/base_controller.php';
require_once __DIR__ . '/../models/privacy_model.php';

/**
 * Privacy Policy Controller
 * Handles privacy policy content related actions
 */
class PrivacyController extends BaseController {
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct();
        $this->model = new PrivacyModel();
        $this->data['page_title'] = 'Privacy Policy | RIGCERT Admin';
    }
    
    /**
     * Index action - display privacy policy content sections list
     */
    public function index() {
        // Get all privacy policy content sections
        $this->data['sections'] = $this->model->getAllSections();
        
        // Render privacy policy content list view
        $this->render('privacy/index', $this->data);
    }
    
    /**
     * Add action - add new privacy policy content section
     */
    public function add() {
        // Set page title
        $this->data['page_title'] = 'Add Privacy Policy Section | RIGCERT Admin';
        
        // Initialize content data
        $this->data['content'] = [
            'id' => '',
            'section' => '',
            'title_en' => '',
            'title_ro' => '',
            'content_en' => '',
            'content_ro' => '',
            'order_number' => $this->getNextOrderNumber(),
            'active' => 1
        ];
        
        // Handle form submission
        if ($this->isPost()) {
            $this->processContentForm();
        }
        
        // Render privacy policy content form view
        $this->render('privacy/form', $this->data);
    }
    
    /**
     * Edit action - edit existing privacy policy content section
     */
    public function edit() {
        // Set page title
        $this->data['page_title'] = 'Edit Privacy Policy Section | RIGCERT Admin';
        
        // Get content ID
        $id = $_GET['id'] ?? 0;
        
        // Get content data
        $content = $this->model->getContentById($id);
        
        if (!$content) {
            // Content not found, redirect to content list
            $this->setFlash('error', 'Privacy policy section not found');
            $this->redirect('index.php?controller=privacy&action=index');
        }
        
        // Set content data
        $this->data['content'] = $content;
        
        // Handle form submission
        if ($this->isPost()) {
            $this->processContentForm();
        }
        
        // Render privacy policy content form view
        $this->render('privacy/form', $this->data);
    }
    
    /**
     * Delete action - delete privacy policy content section
     */
    public function delete() {
        // Get content ID
        $id = $_GET['id'] ?? 0;
        
        // Delete content
        if ($this->model->deleteContent($id)) {
            $this->setFlash('success', 'Privacy policy section deleted successfully');
        } else {
            $this->setFlash('error', 'Error deleting privacy policy section');
        }
        
        // Redirect to content list
        $this->redirect('index.php?controller=privacy&action=index');
    }
    
    /**
     * Process content form submission
     */
    private function processContentForm() {
        // Get form data
        $content = [
            'id' => $_POST['id'] ?? '',
            'section' => trim($_POST['section'] ?? ''),
            'title_en' => trim($_POST['title_en'] ?? ''),
            'title_ro' => trim($_POST['title_ro'] ?? ''),
            'content_en' => $_POST['content_en'] ?? '',
            'content_ro' => $_POST['content_ro'] ?? '',
            'order_number' => intval($_POST['order_number'] ?? 0),
            'active' => isset($_POST['active']) ? 1 : 0
        ];
        
        // Validate required fields
        $errors = [];
        
        if (empty($content['section'])) {
            $errors[] = 'Section name is required';
        }
        
        if (empty($content['content_en'])) {
            $errors[] = 'English content is required';
        }
        
        if (empty($content['content_ro'])) {
            $errors[] = 'Romanian content is required';
        }
        
        // Check if section name already exists (for new content or different content)
        if ($this->model->sectionExists($content['section'], $content['id'])) {
            $errors[] = 'Section name already exists';
        }
        
        // If no errors, save content
        if (empty($errors)) {
            if (empty($content['id'])) {
                // Add new content
                if ($this->model->addContent($content)) {
                    $this->setFlash('success', 'Privacy policy section added successfully');
                    $this->redirect('index.php?controller=privacy&action=index');
                } else {
                    $errors[] = 'Error adding privacy policy section';
                }
            } else {
                // Update existing content
                if ($this->model->updateContent($content)) {
                    $this->setFlash('success', 'Privacy policy section updated successfully');
                    $this->redirect('index.php?controller=privacy&action=index');
                } else {
                    $errors[] = 'Error updating privacy policy section';
                }
            }
        }
        
        // Set errors and content data for form redisplay
        $this->data['errors'] = $errors;
        $this->data['content'] = $content;
    }
    
    /**
     * Get next order number
     */
    private function getNextOrderNumber() {
        $maxOrder = getValue("SELECT MAX(order_number) FROM privacy_content");
        return ($maxOrder ?? 0) + 1;
    }
}
