<?php
require_once __DIR__ . '/base_controller.php';
require_once __DIR__ . '/../models/faq_model.php';

/**
 * FAQ Controller
 * Handles FAQ related actions
 */
class FaqController extends BaseController {
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct();
        $this->model = new FaqModel();
        $this->data['page_title'] = 'FAQ Management | RIGCERT Admin';
    }
    
    /**
     * Index action - display FAQ list
     */
    public function index() {
        // Get all FAQ items
        $this->data['faq_items'] = $this->model->getAllFaq();
        
        // Get categories with counts
        $this->data['categories'] = $this->model->getCategoryCounts();
        
        // Render FAQ list view
        $this->render('faq/index', $this->data);
    }
    
    /**
     * Add action - add new FAQ item
     */
    public function add() {
        // Set page title
        $this->data['page_title'] = 'Add FAQ Item | RIGCERT Admin';
        
        // Initialize FAQ data
        $this->data['faq'] = [
            'id' => '',
            'question_en' => '',
            'question_ro' => '',
            'answer_en' => '',
            'answer_ro' => '',
            'category' => 'general',
            'order_number' => $this->model->getNextOrderNumber(),
            'active' => 1
        ];
        
        // Get existing categories
        $this->data['categories'] = $this->model->getCategories();
        
        // Handle form submission
        if ($this->isPost()) {
            $this->processFaqForm();
        }
        
        // Render FAQ form view
        $this->render('faq/form', $this->data);
    }
    
    /**
     * Edit action - edit existing FAQ item
     */
    public function edit() {
        // Set page title
        $this->data['page_title'] = 'Edit FAQ Item | RIGCERT Admin';
        
        // Get FAQ ID
        $id = $_GET['id'] ?? 0;
        
        // Get FAQ data
        $faq = $this->model->getFaqById($id);
        
        if (!$faq) {
            // FAQ not found, redirect to FAQ list
            $this->setFlash('error', 'FAQ item not found');
            $this->redirect('index.php?controller=faq&action=index');
        }
        
        // Set FAQ data
        $this->data['faq'] = $faq;
        
        // Get existing categories
        $this->data['categories'] = $this->model->getCategories();
        
        // Handle form submission
        if ($this->isPost()) {
            $this->processFaqForm();
        }
        
        // Render FAQ form view
        $this->render('faq/form', $this->data);
    }
    
    /**
     * Delete action - delete FAQ item
     */
    public function delete() {
        // Get FAQ ID
        $id = $_GET['id'] ?? 0;
        
        // Delete FAQ
        if ($this->model->deleteFaq($id)) {
            $this->setFlash('success', 'FAQ item deleted successfully');
        } else {
            $this->setFlash('error', 'Error deleting FAQ item');
        }
        
        // Redirect to FAQ list
        $this->redirect('index.php?controller=faq&action=index');
    }
    
    /**
     * Reorder action - handle FAQ reordering via AJAX
     */
    public function reorder() {
        if ($this->isPost() && isset($_POST['order'])) {
            $orderData = [];
            foreach ($_POST['order'] as $position => $id) {
                $orderData[$id] = $position + 1;
            }
            
            if ($this->model->reorderFaq($orderData)) {
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Error reordering FAQ items']);
            }
        } else {
            echo json_encode(['success' => false, 'error' => 'Invalid request']);
        }
        exit;
    }
    
    /**
     * Process FAQ form submission
     */
    private function processFaqForm() {
        // Get form data
        $faq = [
            'id' => $_POST['id'] ?? '',
            'question_en' => trim($_POST['question_en'] ?? ''),
            'question_ro' => trim($_POST['question_ro'] ?? ''),
            'answer_en' => $_POST['answer_en'] ?? '',
            'answer_ro' => $_POST['answer_ro'] ?? '',
            'category' => trim($_POST['category'] ?? 'general'),
            'order_number' => intval($_POST['order_number'] ?? 0),
            'active' => isset($_POST['active']) ? 1 : 0
        ];
        
        // Validate required fields
        $errors = [];
        
        if (empty($faq['question_en'])) {
            $errors[] = 'English question is required';
        }
        
        if (empty($faq['question_ro'])) {
            $errors[] = 'Romanian question is required';
        }
        
        if (empty($faq['answer_en'])) {
            $errors[] = 'English answer is required';
        }
        
        if (empty($faq['answer_ro'])) {
            $errors[] = 'Romanian answer is required';
        }
        
        if (empty($faq['category'])) {
            $errors[] = 'Category is required';
        }
        
        // If no errors, save FAQ
        if (empty($errors)) {
            if (empty($faq['id'])) {
                // Add new FAQ
                if ($this->model->addFaq($faq)) {
                    $this->setFlash('success', 'FAQ item added successfully');
                    $this->redirect('index.php?controller=faq&action=index');
                } else {
                    $errors[] = 'Error adding FAQ item';
                }
            } else {
                // Update existing FAQ
                if ($this->model->updateFaq($faq)) {
                    $this->setFlash('success', 'FAQ item updated successfully');
                    $this->redirect('index.php?controller=faq&action=index');
                } else {
                    $errors[] = 'Error updating FAQ item';
                }
            }
        }
        
        // Set errors and FAQ data for form redisplay
        $this->data['errors'] = $errors;
        $this->data['faq'] = $faq;
    }
}
