/* Admin Dashboard Styles */

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #f1f1f1;
    padding: .5rem 1rem;
    margin-bottom: .25rem;
}

.sidebar .nav-link.active {
    color: rgb(252 219 0);
}

.sidebar .nav-link:hover {
    color: rgb(252 219 0);
}

.sidebar .nav-link i {
    margin-right: .5rem;
}

/* Cards */
.card {
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e3e6f0;
}

.border-left-primary {
    border-left: .25rem solid #4e73df !important;
}

.border-left-success {
    border-left: .25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: .25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: .25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: .25rem solid #e74a3b !important;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.table th {
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-control-label {
    font-weight: 500;
}

.required::after {
    content: " *";
    color: #e74a3b;
}

/* Preview image */
.preview-image {
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
}

#preview-container {
    display: none;
    margin-top: 1rem;
}

/* Summernote editor */
.note-editor {
    margin-bottom: 1rem;
}

/* Language tabs */
.language-tabs {
    margin-bottom: 1rem;
}

.language-tabs .nav-link {
    padding: .5rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: .25rem .25rem 0 0;
    margin-right: .25rem;
}

.language-tabs .nav-link.active {
    background-color: #f8f9fa;
    border-bottom-color: transparent;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 1rem;
    border-radius: 0 0 .25rem .25rem;
}

/* Utilities */
.text-xs {
    font-size: .7rem;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* Responsive */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        padding-top: 0;
    }
    
    .sidebar-sticky {
        height: auto;
    }
    
    .language-tabs .nav-link {
        padding: .25rem .5rem;
        font-size: .875rem;
    }
}
