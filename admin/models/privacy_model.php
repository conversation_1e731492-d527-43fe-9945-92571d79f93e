<?php
/**
 * Privacy Policy Model
 * Handles privacy policy content related database operations
 */
class PrivacyModel {
    /**
     * Get all privacy policy sections
     * 
     * @return array All privacy policy sections
     */
    public function getAllSections() {
        $stmt = query("SELECT * FROM privacy_content ORDER BY order_number ASC, id ASC");
        return $stmt->fetchAll();
    }
    
    /**
     * Get privacy policy content by section
     * 
     * @param string $section Section name
     * @return array|false Privacy policy content data or false if not found
     */
    public function getSectionByName($section) {
        $stmt = query("SELECT * FROM privacy_content WHERE section = :section", [
            'section' => $section
        ]);
        return $stmt->fetch();
    }
    
    /**
     * Get privacy policy content by ID
     * 
     * @param int $id Content ID
     * @return array|false Privacy policy content data or false if not found
     */
    public function getContentById($id) {
        $stmt = query("SELECT * FROM privacy_content WHERE id = :id", [
            'id' => $id
        ]);
        return $stmt->fetch();
    }
    
    /**
     * Add new privacy policy content section
     * 
     * @param array $content Content data
     * @return bool True on success, false on failure
     */
    public function addContent($content) {
        try {
            query("INSERT INTO privacy_content (section, title_en, title_ro, content_en, content_ro, 
                    order_number, active) 
                    VALUES (:section, :title_en, :title_ro, :content_en, :content_ro, 
                    :order_number, :active)", [
                'section' => $content['section'],
                'title_en' => $content['title_en'],
                'title_ro' => $content['title_ro'],
                'content_en' => $content['content_en'],
                'content_ro' => $content['content_ro'],
                'order_number' => $content['order_number'],
                'active' => $content['active']
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error adding privacy policy content: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update existing privacy policy content
     * 
     * @param array $content Content data
     * @return bool True on success, false on failure
     */
    public function updateContent($content) {
        try {
            query("UPDATE privacy_content SET 
                    section = :section,
                    title_en = :title_en, 
                    title_ro = :title_ro, 
                    content_en = :content_en, 
                    content_ro = :content_ro, 
                    order_number = :order_number, 
                    active = :active 
                    WHERE id = :id", [
                'id' => $content['id'],
                'section' => $content['section'],
                'title_en' => $content['title_en'],
                'title_ro' => $content['title_ro'],
                'content_en' => $content['content_en'],
                'content_ro' => $content['content_ro'],
                'order_number' => $content['order_number'],
                'active' => $content['active']
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error updating privacy policy content: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete privacy policy content
     * 
     * @param int $id Content ID
     * @return bool True on success, false on failure
     */
    public function deleteContent($id) {
        try {
            query("DELETE FROM privacy_content WHERE id = :id", [
                'id' => $id
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error deleting privacy policy content: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get active privacy policy content sections
     * 
     * @return array Active privacy policy content sections
     */
    public function getActiveSections() {
        $stmt = query("SELECT * FROM privacy_content WHERE active = 1 ORDER BY order_number ASC, id ASC");
        return $stmt->fetchAll();
    }
    
    /**
     * Check if section name exists
     * 
     * @param string $section Section name
     * @param int $excludeId ID to exclude from check (for updates)
     * @return bool True if exists, false otherwise
     */
    public function sectionExists($section, $excludeId = 0) {
        $sql = "SELECT COUNT(*) FROM privacy_content WHERE section = :section";
        $params = ['section' => $section];
        
        if ($excludeId > 0) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $count = getValue($sql, $params);
        return $count > 0;
    }
    
    /**
     * Generate unique section name
     * 
     * @param string $title Title to generate section from
     * @return string Unique section name
     */
    public function generateSectionName($title) {
        // Convert title to section name
        $section = strtolower(trim($title));
        $section = preg_replace('/[^a-z0-9]+/', '_', $section);
        $section = trim($section, '_');
        
        // Check if section exists and make it unique
        $originalSection = $section;
        $counter = 1;
        
        while ($this->sectionExists($section)) {
            $section = $originalSection . '_' . $counter;
            $counter++;
        }
        
        return $section;
    }
}
