<?php
/**
 * About Content Model
 * Handles about content related database operations
 */
class AboutModel {
    /**
     * Get all about content sections
     * 
     * @return array All about content sections
     */
    public function getAllSections() {
        $stmt = query("SELECT * FROM about_content ORDER BY order_number ASC, id ASC");
        return $stmt->fetchAll();
    }
    
    /**
     * Get about content by section
     * 
     * @param string $section Section name
     * @return array|false About content data or false if not found
     */
    public function getSectionByName($section) {
        $stmt = query("SELECT * FROM about_content WHERE section = :section", [
            'section' => $section
        ]);
        return $stmt->fetch();
    }
    
    /**
     * Get about content by ID
     * 
     * @param int $id Content ID
     * @return array|false About content data or false if not found
     */
    public function getContentById($id) {
        $stmt = query("SELECT * FROM about_content WHERE id = :id", [
            'id' => $id
        ]);
        return $stmt->fetch();
    }
    
    /**
     * Add new about content section
     * 
     * @param array $content Content data
     * @return bool True on success, false on failure
     */
    public function addContent($content) {
        try {
            query("INSERT INTO about_content (section, title_en, title_ro, content_en, content_ro, 
                    image, order_number, active) 
                    VALUES (:section, :title_en, :title_ro, :content_en, :content_ro, 
                    :image, :order_number, :active)", [
                'section' => $content['section'],
                'title_en' => $content['title_en'],
                'title_ro' => $content['title_ro'],
                'content_en' => $content['content_en'],
                'content_ro' => $content['content_ro'],
                'image' => $content['image'],
                'order_number' => $content['order_number'],
                'active' => $content['active']
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error adding about content: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update existing about content
     * 
     * @param array $content Content data
     * @return bool True on success, false on failure
     */
    public function updateContent($content) {
        try {
            query("UPDATE about_content SET 
                    section = :section,
                    title_en = :title_en, 
                    title_ro = :title_ro, 
                    content_en = :content_en, 
                    content_ro = :content_ro, 
                    image = :image, 
                    order_number = :order_number, 
                    active = :active 
                    WHERE id = :id", [
                'id' => $content['id'],
                'section' => $content['section'],
                'title_en' => $content['title_en'],
                'title_ro' => $content['title_ro'],
                'content_en' => $content['content_en'],
                'content_ro' => $content['content_ro'],
                'image' => $content['image'],
                'order_number' => $content['order_number'],
                'active' => $content['active']
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error updating about content: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete about content
     * 
     * @param int $id Content ID
     * @return bool True on success, false on failure
     */
    public function deleteContent($id) {
        try {
            query("DELETE FROM about_content WHERE id = :id", [
                'id' => $id
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error deleting about content: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get active about content sections
     * 
     * @return array Active about content sections
     */
    public function getActiveSections() {
        $stmt = query("SELECT * FROM about_content WHERE active = 1 ORDER BY order_number ASC, id ASC");
        return $stmt->fetchAll();
    }
    
    /**
     * Check if section name exists
     * 
     * @param string $section Section name
     * @param int $excludeId ID to exclude from check (for updates)
     * @return bool True if exists, false otherwise
     */
    public function sectionExists($section, $excludeId = 0) {
        $sql = "SELECT COUNT(*) FROM about_content WHERE section = :section";
        $params = ['section' => $section];
        
        if ($excludeId > 0) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $count = getValue($sql, $params);
        return $count > 0;
    }
    
    /**
     * Generate unique section name
     * 
     * @param string $title Title to generate section from
     * @return string Unique section name
     */
    public function generateSectionName($title) {
        // Convert title to section name
        $section = strtolower(trim($title));
        $section = preg_replace('/[^a-z0-9]+/', '_', $section);
        $section = trim($section, '_');
        
        // Check if section exists and make it unique
        $originalSection = $section;
        $counter = 1;
        
        while ($this->sectionExists($section)) {
            $section = $originalSection . '_' . $counter;
            $counter++;
        }
        
        return $section;
    }
}
