<?php
/**
 * FAQ Model
 * Handles FAQ related database operations
 */
class FaqModel {
    /**
     * Get all FAQ items
     * 
     * @return array All FAQ items
     */
    public function getAllFaq() {
        $stmt = query("SELECT * FROM faq ORDER BY order_number ASC, id ASC");
        return $stmt->fetchAll();
    }
    
    /**
     * Get FAQ by ID
     * 
     * @param int $id FAQ ID
     * @return array|false FAQ data or false if not found
     */
    public function getFaqById($id) {
        $stmt = query("SELECT * FROM faq WHERE id = :id", [
            'id' => $id
        ]);
        return $stmt->fetch();
    }
    
    /**
     * Get FAQ by category
     * 
     * @param string $category Category name
     * @return array FAQ items in category
     */
    public function getFaqByCategory($category) {
        $stmt = query("SELECT * FROM faq WHERE category = :category AND active = 1 ORDER BY order_number ASC, id ASC", [
            'category' => $category
        ]);
        return $stmt->fetchAll();
    }
    
    /**
     * Add new FAQ item
     * 
     * @param array $faq FAQ data
     * @return bool True on success, false on failure
     */
    public function addFaq($faq) {
        try {
            query("INSERT INTO faq (question_en, question_ro, answer_en, answer_ro, 
                    category, order_number, active) 
                    VALUES (:question_en, :question_ro, :answer_en, :answer_ro, 
                    :category, :order_number, :active)", [
                'question_en' => $faq['question_en'],
                'question_ro' => $faq['question_ro'],
                'answer_en' => $faq['answer_en'],
                'answer_ro' => $faq['answer_ro'],
                'category' => $faq['category'],
                'order_number' => $faq['order_number'],
                'active' => $faq['active']
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error adding FAQ: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update existing FAQ item
     * 
     * @param array $faq FAQ data
     * @return bool True on success, false on failure
     */
    public function updateFaq($faq) {
        try {
            query("UPDATE faq SET 
                    question_en = :question_en, 
                    question_ro = :question_ro, 
                    answer_en = :answer_en, 
                    answer_ro = :answer_ro, 
                    category = :category, 
                    order_number = :order_number, 
                    active = :active 
                    WHERE id = :id", [
                'id' => $faq['id'],
                'question_en' => $faq['question_en'],
                'question_ro' => $faq['question_ro'],
                'answer_en' => $faq['answer_en'],
                'answer_ro' => $faq['answer_ro'],
                'category' => $faq['category'],
                'order_number' => $faq['order_number'],
                'active' => $faq['active']
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error updating FAQ: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete FAQ item
     * 
     * @param int $id FAQ ID
     * @return bool True on success, false on failure
     */
    public function deleteFaq($id) {
        try {
            query("DELETE FROM faq WHERE id = :id", [
                'id' => $id
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error deleting FAQ: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get active FAQ items
     * 
     * @return array Active FAQ items
     */
    public function getActiveFaq() {
        $stmt = query("SELECT * FROM faq WHERE active = 1 ORDER BY order_number ASC, id ASC");
        return $stmt->fetchAll();
    }
    
    /**
     * Get all FAQ categories
     * 
     * @return array List of categories
     */
    public function getCategories() {
        $stmt = query("SELECT DISTINCT category FROM faq WHERE active = 1 ORDER BY category ASC");
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Get FAQ count by category
     * 
     * @return array Category counts
     */
    public function getCategoryCounts() {
        $stmt = query("SELECT category, COUNT(*) as count FROM faq WHERE active = 1 GROUP BY category ORDER BY category ASC");
        return $stmt->fetchAll();
    }
    
    /**
     * Get next order number for a category
     * 
     * @param string $category Category name
     * @return int Next order number
     */
    public function getNextOrderNumber($category = null) {
        if ($category) {
            $maxOrder = getValue("SELECT MAX(order_number) FROM faq WHERE category = :category", [
                'category' => $category
            ]);
        } else {
            $maxOrder = getValue("SELECT MAX(order_number) FROM faq");
        }
        
        return ($maxOrder ?? 0) + 1;
    }
    
    /**
     * Reorder FAQ items
     * 
     * @param array $orderData Array of id => order_number pairs
     * @return bool True on success, false on failure
     */
    public function reorderFaq($orderData) {
        try {
            foreach ($orderData as $id => $orderNumber) {
                query("UPDATE faq SET order_number = :order_number WHERE id = :id", [
                    'id' => $id,
                    'order_number' => $orderNumber
                ]);
            }
            return true;
        } catch (Exception $e) {
            error_log('Error reordering FAQ: ' . $e->getMessage());
            return false;
        }
    }
}
