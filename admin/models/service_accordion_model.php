<?php
/**
 * Service Accordion Model
 * Handles service accordion related database operations
 */
class ServiceAccordionModel {
    /**
     * Get all accordions for a specific service
     * 
     * @param int $serviceId Service ID
     * @return array Service accordions
     */
    public function getAccordionsByService($serviceId) {
        $stmt = query("SELECT * FROM service_accordions WHERE service_id = :service_id ORDER BY order_number ASC, id ASC", [
            'service_id' => $serviceId
        ]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get active accordions for a specific service
     * 
     * @param int $serviceId Service ID
     * @return array Active service accordions
     */
    public function getActiveAccordionsByService($serviceId) {
        $stmt = query("SELECT * FROM service_accordions WHERE service_id = :service_id AND active = 1 ORDER BY order_number ASC, id ASC", [
            'service_id' => $serviceId
        ]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get accordion by ID
     * 
     * @param int $id Accordion ID
     * @return array|false Accordion data or false if not found
     */
    public function getAccordionById($id) {
        $stmt = query("SELECT * FROM service_accordions WHERE id = :id", [
            'id' => $id
        ]);
        return $stmt->fetch();
    }
    
    /**
     * Add new service accordion
     * 
     * @param array $accordion Accordion data
     * @return bool True on success, false on failure
     */
    public function addAccordion($accordion) {
        try {
            query("INSERT INTO service_accordions (service_id, title_en, title_ro, content_en, content_ro, 
                    order_number, active) 
                    VALUES (:service_id, :title_en, :title_ro, :content_en, :content_ro, 
                    :order_number, :active)", [
                'service_id' => $accordion['service_id'],
                'title_en' => $accordion['title_en'],
                'title_ro' => $accordion['title_ro'],
                'content_en' => $accordion['content_en'],
                'content_ro' => $accordion['content_ro'],
                'order_number' => $accordion['order_number'],
                'active' => $accordion['active']
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error adding service accordion: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update existing service accordion
     * 
     * @param array $accordion Accordion data
     * @return bool True on success, false on failure
     */
    public function updateAccordion($accordion) {
        try {
            query("UPDATE service_accordions SET 
                    service_id = :service_id,
                    title_en = :title_en, 
                    title_ro = :title_ro, 
                    content_en = :content_en, 
                    content_ro = :content_ro, 
                    order_number = :order_number, 
                    active = :active 
                    WHERE id = :id", [
                'id' => $accordion['id'],
                'service_id' => $accordion['service_id'],
                'title_en' => $accordion['title_en'],
                'title_ro' => $accordion['title_ro'],
                'content_en' => $accordion['content_en'],
                'content_ro' => $accordion['content_ro'],
                'order_number' => $accordion['order_number'],
                'active' => $accordion['active']
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error updating service accordion: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete service accordion
     * 
     * @param int $id Accordion ID
     * @return bool True on success, false on failure
     */
    public function deleteAccordion($id) {
        try {
            query("DELETE FROM service_accordions WHERE id = :id", [
                'id' => $id
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error deleting service accordion: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get next order number for a service
     * 
     * @param int $serviceId Service ID
     * @return int Next order number
     */
    public function getNextOrderNumber($serviceId) {
        $maxOrder = getValue("SELECT MAX(order_number) FROM service_accordions WHERE service_id = :service_id", [
            'service_id' => $serviceId
        ]);
        
        return ($maxOrder ?? 0) + 1;
    }
    
    /**
     * Reorder service accordions
     * 
     * @param array $orderData Array of id => order_number pairs
     * @return bool True on success, false on failure
     */
    public function reorderAccordions($orderData) {
        try {
            foreach ($orderData as $id => $orderNumber) {
                query("UPDATE service_accordions SET order_number = :order_number WHERE id = :id", [
                    'id' => $id,
                    'order_number' => $orderNumber
                ]);
            }
            return true;
        } catch (Exception $e) {
            error_log('Error reordering service accordions: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get accordion count for a service
     * 
     * @param int $serviceId Service ID
     * @return int Number of accordions
     */
    public function getAccordionCount($serviceId) {
        return getValue("SELECT COUNT(*) FROM service_accordions WHERE service_id = :service_id", [
            'service_id' => $serviceId
        ]);
    }
    
    /**
     * Get all accordions with service information
     * 
     * @return array All accordions with service data
     */
    public function getAllAccordionsWithServices() {
        $stmt = query("SELECT sa.*, s.title_en as service_title_en, s.title_ro as service_title_ro 
                       FROM service_accordions sa 
                       LEFT JOIN services s ON sa.service_id = s.id 
                       ORDER BY s.title_en ASC, sa.order_number ASC, sa.id ASC");
        return $stmt->fetchAll();
    }
    
    /**
     * Delete all accordions for a service
     * 
     * @param int $serviceId Service ID
     * @return bool True on success, false on failure
     */
    public function deleteAccordionsByService($serviceId) {
        try {
            query("DELETE FROM service_accordions WHERE service_id = :service_id", [
                'service_id' => $serviceId
            ]);
            return true;
        } catch (Exception $e) {
            error_log('Error deleting service accordions: ' . $e->getMessage());
            return false;
        }
    }
}
