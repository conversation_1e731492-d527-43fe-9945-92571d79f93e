<?php
// Admin dashboard main entry point
session_start();
require_once __DIR__ . '/../config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id']) && basename($_SERVER['PHP_SELF']) !== 'login.php') {
    header('Location: login.php');
    exit;
}

// Set default controller and action
$controller = $_GET['controller'] ?? 'dashboard';
$action = $_GET['action'] ?? 'index';

// Define allowed controllers and actions
$allowedControllers = [
    'dashboard' => ['index'],
    'services' => ['index', 'add', 'edit', 'delete'],
    'serviceAccordion' => ['index', 'add', 'edit', 'delete', 'reorder'],
    'news' => ['index', 'add', 'edit', 'delete'],
    'about' => ['index', 'add', 'edit', 'delete'],
    'faq' => ['index', 'add', 'edit', 'delete', 'reorder'],
    'translations' => ['index', 'edit'],
    'users' => ['index', 'add', 'edit', 'delete', 'profile'],
    'media' => ['index', 'upload', 'delete']
];

// Validate controller and action
if (!isset($allowedControllers[$controller]) || !in_array($action, $allowedControllers[$controller])) {
    $controller = 'dashboard';
    $action = 'index';
}

// Include the controller file
$controllerFile = __DIR__ . '/controllers/' . $controller . '_controller.php';
//die("Controller file: $controllerFile");
if (file_exists($controllerFile)) {
    require_once $controllerFile;

    // Create controller instance and call action
    $controllerClass = ucfirst($controller) . 'Controller';
    $controllerInstance = new $controllerClass();
    $controllerInstance->$action();
} else {
    // Controller not found, redirect to dashboard
    header('Location: index.php?controller=dashboard&action=index');
    exit;
}
