<?php
require_once __DIR__ . "/../vendor/autoload.php";
require_once __DIR__ . "/db.php";

//const ROOT_URL   = 'https://gg.digitalreputation.ro/rigcert';
const ROOT_URL   = 'https://rigcert.test';
const ASSETS_URL = ROOT_URL . '/assets';
const ADMIN_ROOT = ROOT_URL . '/admin/';
//const ADMIN_ROOT = 'https://gg.digitalreputation.ro/rigcert/admin';

/* === LANGUAGE/CONTENT SETTINGS === */
$lang = 'ro';
if (isset($_GET['lang']) && in_array($_GET['lang'], ['ro', 'en'])) {
	setcookie('rigcert_lang', $_GET['lang'], time() + (86400 * 30 * 30), "/");
	$_COOKIE['rigcert_lang'] = $_GET['lang'];
	$lang                     = $_GET['lang'];
}
if (isset($_COOKIE['rigcert_lang']) && in_array($_COOKIE['rigcert_lang'], ['ro', 'en'])) {
	$lang = $_COOKIE['rigcert_lang'];
}

define('LANG', $lang);
require_once __DIR__ . "/LazyContentLoader.php";
require_once __DIR__ . "/helpers.php";
$content = new LazyContentLoader(LANG);

//function slugify($title, $id = 0) {
//	// Remove special characters
//	$slug = preg_replace('/[^A-Za-z0-9-]+/', '-', $title);
//	// Convert to lowercase
//	$slug = strtolower($slug);
//	// Remove leading and trailing hyphens
//	$slug = trim($slug, '-');
//	if ($id) {
//		$slug .= "-" . $id;
//	}
//	return $slug;
//}