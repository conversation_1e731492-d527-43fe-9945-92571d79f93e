<?php
/**
 * Helper functions for the website
 */

/**
 * Get services from database
 * 
 * @param int $limit Number of services to get (0 for all)
 * @return array Services
 */
function getServices($limit = 0) {
    require_once __DIR__ . '/../admin/models/services_model.php';
    $servicesModel = new ServicesModel();
    return $servicesModel->getActiveServices($limit);
}

/**
 * Get service by slug
 * 
 * @param string $slug Service slug
 * @return array|false Service data or false if not found
 */
function getServiceBySlug($slug) {
    require_once __DIR__ . '/../admin/models/services_model.php';
    $servicesModel = new ServicesModel();
    return $servicesModel->getServiceBySlug($slug);
}

/**
 * Get news from database
 * 
 * @param int $limit Number of news to get (0 for all)
 * @return array News
 */
function getNews($limit = 0) {
    require_once __DIR__ . '/../admin/models/news_model.php';
    $newsModel = new NewsModel();
    return $newsModel->getActiveNews($limit);
}

/**
 * Get news by slug
 * 
 * @param string $slug News slug
 * @return array|false News data or false if not found
 */
function getNewsBySlug($slug) {
    require_once __DIR__ . '/../admin/models/news_model.php';
    $newsModel = new NewsModel();
    return $newsModel->getNewsBySlug($slug);
}

/**
 * Get recent news
 * 
 * @param int $limit Number of news to get
 * @return array Recent news
 */
function getRecentNews($limit = 3) {
    require_once __DIR__ . '/../admin/models/news_model.php';
    $newsModel = new NewsModel();
    return $newsModel->getRecentNews($limit);
}

/**
 * Get translation
 * 
 * @param string $key Translation key
 * @param string $default Default value if translation not found
 * @return string Translation
 */
function t($key, $default = '') {
    global $content;
	// if key is not set in content, log an error
	if (!isset($content[$key])) {
		error_log("Translation key '$key' not found in content for language '" . getCurrentLanguage() . "'");
	}
    return $content[$key] ?? $default ?: $key;
}

/**
 * Generate slug from title
 * 
 * @param string $title Title to slugify
 * @param int $id ID to append to slug
 * @return string Slug
 */
function slugify($title, $id = 0) {
    // Remove special characters
    $slug = preg_replace('/[^A-Za-z0-9-]+/', '-', $title);
    // Convert to lowercase
    $slug = strtolower($slug);
    // Remove leading and trailing hyphens
    $slug = trim($slug, '-');
    if ($id) {
        $slug .= "-" . $id;
    }
    return $slug;
}

/**
 * Format date
 * 
 * @param string $date Date to format
 * @param string $format Format to use
 * @return string Formatted date
 */
function formatDate($date, $format = 'j F Y') {
    return date($format, strtotime($date));
}

/**
 * Truncate text
 * 
 * @param string $text Text to truncate
 * @param int $length Length to truncate to
 * @param string $suffix Suffix to add if truncated
 * @return string Truncated text
 */
function truncate($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . $suffix;
}

/**
 * Get current language
 * 
 * @return string Current language
 */
function getCurrentLanguage() {
    return defined('LANG') ? LANG : 'en';
}

/**
 * Get language URL
 * 
 * @param string $lang Language code
 * @return string Language URL
 */
function getLanguageUrl($lang) {
    $route = $_GET['route'] ?? '';
    return ROOT_URL . '/' . $lang . ($route ? '/' . $route : '');
}

/**
 * Check if current page is active
 * 
 * @param string $page Page to check
 * @return bool True if current page is active
 */
function isActivePage($page) {
    $route = $_GET['route'] ?? '';
    return $route === $page || strpos($route, $page . '/') === 0;
}

/**
 * Get active class if current page is active
 * 
 * @param string $page Page to check
 * @param string $class Class to add if active
 * @return string Class if active, empty string if not
 */
function activeClass($page, $class = 'active') {
    return isActivePage($page) ? $class : '';
}
