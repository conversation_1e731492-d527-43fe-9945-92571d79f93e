# WYSIWYG Content Management System Implementation

## Overview
This document describes the implementation of WYSIWYG content management for About Us content and FAQ management in the RIGCERT admin system.

## Features Implemented

### 1. About Us Content Management
- **WYSIWYG Editor**: Full Summernote integration for rich text editing
- **Multi-language Support**: English and Romanian content management
- **Section-based Organization**: Multiple content sections with unique identifiers
- **Image Upload**: Support for section images with automatic file handling
- **Order Management**: Control display sequence with order numbers
- **Active/Inactive Status**: Toggle content visibility

### 2. FAQ Management System
- **Dynamic FAQ Items**: Create unlimited FAQ items with rich content
- **Category Organization**: Group FAQ items by categories
- **Drag & Drop Reordering**: Visual reordering with AJAX updates
- **Multi-language Support**: Questions and answers in both languages
- **WYSIWYG Answers**: Rich text formatting for comprehensive answers
- **Active/Inactive Status**: Control FAQ visibility

## Database Schema

### About Content Table (`about_content`)
```sql
CREATE TABLE `about_content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `section` varchar(100) NOT NULL,
  `title_en` varchar(255) DEFAULT NULL,
  `title_ro` varchar(255) DEFAULT NULL,
  `content_en` longtext NOT NULL,
  `content_ro` longtext NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `order_number` int(11) DEFAULT '0',
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `section` (`section`)
);
```

### FAQ Table (`faq`)
```sql
CREATE TABLE `faq` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_en` text NOT NULL,
  `question_ro` text NOT NULL,
  `answer_en` longtext NOT NULL,
  `answer_ro` longtext NOT NULL,
  `category` varchar(100) DEFAULT 'general',
  `order_number` int(11) DEFAULT '0',
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

## Files Created/Modified

### New Files Created:
1. **Database Schema**: `admin/db_schema_update.sql`
2. **Models**:
   - `admin/models/about_model.php`
   - `admin/models/faq_model.php`
3. **Controllers**:
   - `admin/controllers/about_controller.php`
   - `admin/controllers/faq_controller.php`
4. **Admin Views**:
   - `admin/views/about/index.php`
   - `admin/views/about/form.php`
   - `admin/views/faq/index.php`
   - `admin/views/faq/form.php`

### Modified Files:
1. **Admin Navigation**: `admin/views/layouts/header.php`
2. **Admin Router**: `admin/index.php`
3. **Frontend Pages**:
   - `pages/about-us.php`
   - `pages/sections/faq-section.php`

## Installation Instructions

### 1. Database Setup
```bash
# Run the database schema update
mysql -u username -p database_name < admin/db_schema_update.sql
```

### 2. File Permissions
```bash
# Create upload directory for about images
mkdir -p uploads/about
chmod 755 uploads/about
```

### 3. Admin Access
- Navigate to `/admin` in your browser
- Login with admin credentials
- New menu items "About Content" and "FAQ" will be available

## Usage Guide

### About Content Management

#### Adding New Content Section:
1. Go to **Admin → About Content**
2. Click **"Add New Section"**
3. Fill in the form:
   - **Section Name**: Unique identifier (e.g., `main_content`, `our_mission`)
   - **Title**: Optional titles in both languages
   - **Content**: Rich text content using WYSIWYG editor
   - **Image**: Optional section image
   - **Order Number**: Controls display sequence
   - **Active**: Toggle visibility

#### Editing Content:
1. Click the edit icon next to any section
2. Modify content using the WYSIWYG editor
3. Save changes

#### Content Display:
- Active sections appear on the About Us page
- Sections are ordered by `order_number`
- Content displays based on current language setting
- Images are automatically responsive

### FAQ Management

#### Adding FAQ Items:
1. Go to **Admin → FAQ**
2. Click **"Add New FAQ"**
3. Fill in the form:
   - **Question**: In both English and Romanian
   - **Answer**: Rich text answers using WYSIWYG editor
   - **Category**: Group related questions
   - **Order Number**: Controls display sequence
   - **Active**: Toggle visibility

#### Reordering FAQ Items:
1. On the FAQ index page, drag items using the grip icon
2. Order is automatically saved via AJAX
3. Changes reflect immediately on the frontend

#### Category Management:
- Select from predefined categories or create new ones
- Categories help organize related questions
- FAQ items display grouped by category

### Frontend Integration

#### About Us Page:
- Dynamically loads content from `about_content` table
- Falls back to static translations if no database content
- Supports multiple sections with images
- Responsive design maintained

#### FAQ Section:
- Dynamically loads from `faq` table
- Maintains original accordion layout
- Two-column responsive design
- Falls back to static content if needed

## Technical Features

### WYSIWYG Editor (Summernote):
- Rich text formatting (bold, italic, lists, etc.)
- Link insertion and management
- Image embedding support
- HTML source editing
- Responsive toolbar

### Image Upload:
- Automatic file validation (type and size)
- Unique filename generation
- Secure upload handling
- Thumbnail display in admin

### Multi-language Support:
- Content stored in both languages
- Automatic language switching based on `LANG` constant
- Fallback to English if Romanian content missing

### Security Features:
- Input validation and sanitization
- File upload restrictions
- SQL injection prevention
- XSS protection with htmlspecialchars

### Performance Optimizations:
- Efficient database queries
- Minimal frontend impact
- Graceful fallbacks
- Error handling

## Customization Options

### Adding New Categories:
FAQ categories can be extended by:
1. Adding options in the form dropdown
2. Categories are automatically created when used
3. No database schema changes needed

### Extending About Sections:
New about sections can be added with:
1. Unique section identifiers
2. Custom ordering
3. Optional images and titles

### Styling Customization:
- Admin interface uses Bootstrap 5
- Frontend maintains existing CSS classes
- WYSIWYG content inherits site styles

## Troubleshooting

### Common Issues:

1. **Upload Directory Permissions**:
   ```bash
   chmod 755 uploads/about
   chown www-data:www-data uploads/about
   ```

2. **Database Connection**:
   - Verify database credentials in config
   - Ensure tables are created properly

3. **WYSIWYG Not Loading**:
   - Check Summernote CSS/JS includes
   - Verify jQuery is loaded

4. **Content Not Displaying**:
   - Check `active` status in admin
   - Verify language settings
   - Check for PHP errors in logs

## Future Enhancements

Potential improvements:
1. **Content Versioning**: Track content changes
2. **Media Library**: Centralized image management
3. **SEO Fields**: Meta descriptions for sections
4. **Content Scheduling**: Publish/unpublish dates
5. **Advanced Permissions**: Role-based content access
6. **Content Templates**: Predefined section layouts
7. **Export/Import**: Content backup and migration

## Support

For technical support or questions about this implementation:
1. Check error logs for specific issues
2. Verify database connectivity and permissions
3. Ensure all required files are uploaded
4. Test with different user roles and permissions

The system is designed to be robust and user-friendly, providing a complete content management solution for the RIGCERT website's About Us and FAQ sections.
