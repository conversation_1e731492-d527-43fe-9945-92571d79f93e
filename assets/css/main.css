/*
============================
Name:  ADVICX - Business Consulting Theme
Version: 1.0.0
Description:
Author:  VikingLab
Author URI: https://themeforest.net/user/vikinglab/portfolio
Location:
============================
*/
/*

CSS LIST

=<::::::::::::::::::::::::::=>
  HEADER AREA CSS
=<::::::::::::::::::::::::::=>
  NAV MENU AREA CSS
=<::::::::::::::::::::::::::=>
  WELCOME AREA CSS
=<::::::::::::::::::::::::::=>
  ABOUT AREA CSS
=<::::::::::::::::::::::::::=>
  SERVICE AREA CSS
=<::::::::::::::::::::::::::=>
  ACCOUNT AREA CSS
=<::::::::::::::::::::::::::=>
  FEATURES AREA CSS
=<::::::::::::::::::::::::::=>
  BRAND AREA CSS
=<::::::::::::::::::::::::::=>
  CHOOSE AREA CSS
=<::::::::::::::::::::::::::=>
  PRRELOADER AREA CSS
=<::::::::::::::::::::::::::=>
  PRICING AREA CSS
=<::::::::::::::::::::::::::=>
  TEAM AREA CSS
=<::::::::::::::::::::::::::=>
  TESTIMONIAL AREA CSS
=<::::::::::::::::::::::::::=>
  WORK AREA CSS
=<::::::::::::::::::::::::::=>
  OTHERS AREA CSS
=<::::::::::::::::::::::::::=>
  CONMMON AREA CSS
=<::::::::::::::::::::::::::=>
  BLOG AREA CSS
=<::::::::::::::::::::::::::=>
  CTA AREA CSS
=<::::::::::::::::::::::::::=>
 ANIMATION AREA CSS
=<::::::::::::::::::::::::::=>
  BUTTONS AREA CSS
=<::::::::::::::::::::::::::=>
  TYPOGRAPHY AREA CSS
=<::::::::::::::::::::::::::=>
  FOOTER AREA CSS
=<::::::::::::::::::::::::::=>
*/
/*
::::::::::::::::::::::::::
 TYPOGRAPHY AREA CSS
::::::::::::::::::::::::::
*/
@import url("https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&amp;display=swap");
body {
  font-size: var(--f-fs-font-16);
  font-family: var(--f-ff-font-1);
  font-weight: var(--f-fw-normal);
}

.body1 {
  background-color: var(--vtc-bg-white3);
  overflow-x: hidden;
}

.body2 {
  background-color: var(--vtc-bg-white4);
  overflow-x: hidden;
}

.body5 {
  background-color: var(--vtc-bg-white8);
  overflow-x: hidden;
}

@media screen and (max-width: 769px) {
  body.body, html {
    overflow-x: hidden !important;
  }
}
.img100 img {
  width: 100%;
}

@media screen and (min-width: 769px) {
  body.body.body5 {
    overflow-x: initial !important;
  }
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

input,
textarea,
select,
option {
  max-width: 100%;
}

h1, h2, h3, h4, h5, h6 {
  padding: 0;
  margin: 0;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

p {
  padding: 0;
  margin: 0;
}

img {
  max-width: 100%;
  max-height: 100%;
}

a,
a:hover,
a:focus {
  outline: none;
  text-decoration: none;
}

.sp {
  padding: 100px 0px;
}

.sp2 {
  padding: 120px 0px;
}

.sp3 {
  padding: 80px 0px;
}

@media (max-width: 768px) {
  .sp {
    padding: 50px 0px;
  }
  .sp2 {
    padding: 60px 0px;
  }
  .sp3 {
    padding: 40px 0px;
  }
}
@media (max-width: 768px) {
  .sp {
    padding: 50px 0px;
  }
  .sp2 {
    padding: 60px 0px;
  }
  .sp3 {
    padding: 40px 0px;
  }
}
.space4 {
  height: 4px;
}

.space6 {
  height: 6px;
}

.space28 {
  height: 28px;
}

.space8 {
  height: 8px;
}

.space12 {
  height: 12px;
}

.space14 {
  height: 14px;
}

.space16 {
  height: 16px;
}

.space24 {
  height: 24px;
}

.space32 {
  height: 32px;
}

.space10 {
  height: 10px;
}

.space5 {
  height: 5px;
}

.space5 {
  height: 5px;
}

@media (max-width: 767px) {
  .space10 {
    height: 5px;
  }
  .sp5 {
    padding-bottom: 50px;
  }
}
.space20 {
  height: 20px;
}

@media (max-width: 767px) {
  .space20 {
    height: 10px;
  }
}
.space30 {
  height: 30px;
}

@media (max-width: 767px) {
  .space30 {
    height: 15px;
  }
}
.space40 {
  height: 40px;
}

@media (max-width: 767px) {
  .space40 {
    height: 20px;
  }
}
.space50 {
  height: 50px;
}

.space45 {
  height: 45px;
}

@media (max-width: 768px) {
  .space50 {
    height: 25px;
  }
  .space60 {
    height: 30px !important;
  }
}
.space60 {
  height: 60px;
}

.space55 {
  height: 55px;
}

.space70 {
  height: 70px;
}

.space80 {
  height: 80px;
}

.space90 {
  height: 90px;
}

.space100 {
  height: 100px;
}

.space120 {
  height: 120px;
}

.text-right {
  text-align: right;
}

.section-padding {
  padding: 120px 0;
}

@media (max-width: 991px) {
  .section-padding {
    padding: 60px 0;
  }
}
.section-padding2 {
  padding: 120px 0 90px;
}

@media (max-width: 991px) {
  .section-padding2 {
    padding: 60px 0 30px;
  }
}
.padding-bottom {
  padding-bottom: 120px;
}

@media (max-width: 991px) {
  .padding-bottom {
    padding-bottom: 60px;
  }
}
.padding-bottom2 {
  padding-bottom: 90px;
}

@media (max-width: 991px) {
  .padding-bottom2 {
    padding-bottom: 30px;
  }
}
.padding-top {
  padding-top: 120px;
}

@media (max-width: 991px) {
  .padding-top {
    padding-top: 60px;
  }
}
.padding-top2 {
  padding-top: 90px;
}

@media (max-width: 991px) {
  .padding-top2 {
    padding-top: 30px;
  }
}
.padding-90 {
  padding: 90px 0;
}

@media (max-width: 991px) {
  .padding-90 {
    padding: 50px 0;
  }
}
.w-full {
  width: 100%;
}

@media (min-width: 992px) {
  .w-lg-full {
    width: 100%;
  }
}
.text-center {
  text-align: center;
}

@media (min-width: 992px) {
  .text-lg-center {
    text-align: center;
  }
}
.text-left {
  text-align: left !important;
}

.weight-400 {
  font-weight: 400 !important;
}

.weight-500 {
  font-weight: 500 !important;
}

.weight-600 {
  font-weight: 600 !important;
}

.weight-700 {
  font-weight: 700 !important;
}

.weight-800 {
  font-weight: 800 !important;
}

.weight-900 {
  font-weight: 900 !important;
}

.font-f-1 {
  font-family: "Hind", sans-serif !important;
}

.font-f-2 {
  font-family: "Plus Jakarta Sans", sans-serif !important;
}

.font-f-3 {
  font-family: "Spline Sans", sans-serif !important;
}

.font-f-4 {
  font-family: "Catamaran", sans-serif !important;
}

.font-12 {
  font-size: 12px;
}

.font-14 {
  font-size: 14px;
}

.font-16 {
  font-size: 16px;
}

.font-18 {
  font-size: 18px;
}

.font-20 {
  font-size: 20px;
}

.font-22 {
  font-size: 22px;
}

.font-24 {
  font-size: 24px;
}

.font-26 {
  font-size: 26px;
}

.font-28 {
  font-size: 28px;
}

.font-30 {
  font-size: 30px;
}

.font-32 {
  font-size: 32px;
}

.font-34 {
  font-size: 34px;
}

.font-36 {
  font-size: 36px;
}

.font-40 {
  font-size: 40px;
}

.font-42 {
  font-size: 42px;
}

.font-44 {
  font-size: 44px;
}

.font-46 {
  font-size: 48px;
}

.font-48 {
  font-size: 48px;
}

.font-50 {
  font-size: 60px;
}

.font-52 {
  font-size: 52px;
}

.font-54 {
  font-size: 54px;
}

.font-56 {
  font-size: 56px;
}

.font-58 {
  font-size: 58px;
}

.font-60 {
  font-size: 60px;
}

.font-62 {
  font-size: 62px;
}

.font-70 {
  font-size: 70px;
}

.font-72 {
  font-size: 72px;
}

.font-74 {
  font-size: 74px;
}

.font-76 {
  font-size: 76px;
}

.font-78 {
  font-size: 78px;
}

.font-80 {
  font-size: 80px;
}

.font-82 {
  font-size: 82px;
}

.font-84 {
  font-size: 84px;
}

.font-86 {
  font-size: 86px;
}

.font-88 {
  font-size: 88px;
}

.font-90 {
  font-size: 90px;
}

.font-92 {
  font-size: 92px;
}

.font-94 {
  font-size: 94px;
}

.font-96 {
  font-size: 96px;
}

.font-98 {
  font-size: 98px;
}

.font-100 {
  font-size: 100px;
}

@media screen and (min-width: 1024px) {
  .text-md-right {
    text-align: right;
  }
  .text-md-center {
    text-align: center;
  }
  .text-md-left {
    text-align: left;
  }
  .font-lg-12 {
    font-size: 12px;
  }
  .font-lg-14 {
    font-size: 14px;
  }
  .font-lg-16 {
    font-size: 16px;
  }
  .font-lg-18 {
    font-size: 18px;
  }
  .font-lg-20 {
    font-size: 20px;
  }
  .font-lg-22 {
    font-size: 22px;
  }
  .font-lg-24 {
    font-size: 24px;
  }
  .font-lg-26 {
    font-size: 26px;
  }
  .font-lg-28 {
    font-size: 28px;
  }
  .font-lg-30 {
    font-size: 30px;
  }
  .font-lg-32 {
    font-size: 32px;
  }
  .font-lg-34 {
    font-size: 34px;
  }
  .font-lg-36 {
    font-size: 36px;
  }
  .font-lg-40 {
    font-size: 40px;
  }
  .font-lg-42 {
    font-size: 42px;
  }
  .font-lg-44 {
    font-size: 44px;
  }
  .font-lg-45 {
    font-size: 45px;
  }
  .font-lg-46 {
    font-size: 48px;
  }
  .font-lg-48 {
    font-size: 48px;
  }
  .font-lg-50 {
    font-size: 60px;
  }
  .font-lg-52 {
    font-size: 52px;
  }
  .font-lg-54 {
    font-size: 54px;
  }
  .font-lg-56 {
    font-size: 56px;
  }
  .font-lg-58 {
    font-size: 58px;
  }
  .font-lg-60 {
    font-size: 60px;
  }
  .font-lg-62 {
    font-size: 62px;
  }
  .font-lg-66 {
    font-size: 66px;
  }
  .font-lg-70 {
    font-size: 70px;
  }
  .font-lg-72 {
    font-size: 72px;
  }
  .font-lg-74 {
    font-size: 74px;
  }
  .font-lg-76 {
    font-size: 76px;
  }
  .font-lg-78 {
    font-size: 78px;
  }
  .font-lg-80 {
    font-size: 80px;
  }
  .font-lg-82 {
    font-size: 82px;
  }
  .font-lg-84 {
    font-size: 84px;
  }
  .font-lg-86 {
    font-size: 86px;
  }
  .font-lg-88 {
    font-size: 88px;
  }
  .font-lg-90 {
    font-size: 90px;
  }
  .font-lg-92 {
    font-size: 92px;
  }
  .font-lg-94 {
    font-size: 94px;
  }
  .font-lg-96 {
    font-size: 96px;
  }
  .font-lg-98 {
    font-size: 98px;
  }
  .font-lg-100 {
    font-size: 100px;
  }
  .line-height-lg-14 {
    line-height: 14px;
  }
  .line-height-lg-16 {
    line-height: 16px;
  }
  .line-height-lg-18 {
    line-height: 18px;
  }
  .line-height-lg-20 {
    line-height: 20px;
  }
  .line-height-lg-22 {
    line-height: 22px;
  }
  .line-height-lg-24 {
    line-height: 24px;
  }
  .line-height-lg-26 {
    line-height: 26px;
  }
  .line-height-lg-28 {
    line-height: 28px;
  }
  .line-height-lg-30 {
    line-height: 30px;
  }
  .line-height-lg-32 {
    line-height: 32px;
  }
  .line-height-lg-34 {
    line-height: 34px;
  }
  .line-height-lg-36 {
    line-height: 36px;
  }
  .line-height-lg-38 {
    line-height: 38px;
  }
  .line-height-lg-40 {
    line-height: 40px;
  }
  .line-height-lg-42 {
    line-height: 42px;
  }
  .line-height-lg-44 {
    line-height: 44px;
  }
  .line-height-lg-48 {
    line-height: 48px;
  }
  .line-height-lg-50 {
    line-height: 50px;
  }
  .line-height-lg-52 {
    line-height: 52px;
  }
  .line-height-lg-54 {
    line-height: 54px;
  }
  .line-height-lg-56 {
    line-height: 56px;
  }
  .line-height-lg-58 {
    line-height: 58px;
  }
  .line-height-lg-60 {
    line-height: 60px;
  }
  .line-height-lg-62 {
    line-height: 62px;
  }
  .line-height-lg-64 {
    line-height: 64px;
  }
  .line-height-lg-66 {
    line-height: 66px;
  }
  .line-height-lg-68 {
    line-height: 68px;
  }
  .line-height-lg-70 {
    line-height: 70px;
  }
  .line-height-lg-72 {
    line-height: 72px;
  }
  .line-height-lg-74 {
    line-height: 74px;
  }
  .line-height-lg-76 {
    line-height: 76px;
  }
  .line-height-lg-78 {
    line-height: 78px;
  }
  .line-height-lg-80 {
    line-height: 80px;
  }
  .line-height-lg-82 {
    line-height: 82px;
  }
  .line-height-lg-84 {
    line-height: 84px;
  }
  .line-height-lg-86 {
    line-height: 86px;
  }
  .line-height-lg-88 {
    line-height: 88px;
  }
  .line-height-lg-90 {
    line-height: 90px;
  }
  .line-height-lg-92 {
    line-height: 92px;
  }
  .line-height-lg-94 {
    line-height: 94px;
  }
  .line-height-lg-96 {
    line-height: 96px;
  }
  .line-height-lg-98 {
    line-height: 98px;
  }
  .line-height-lg-100 {
    line-height: 100px;
  }
}
@media screen and (min-width: 768px) {
  .font-md-12 {
    font-size: 12px;
  }
  .font-md-14 {
    font-size: 14px;
  }
  .font-md-16 {
    font-size: 16px;
  }
  .font-md-18 {
    font-size: 18px;
  }
  .font-md-20 {
    font-size: 20px;
  }
  .font-md-22 {
    font-size: 22px;
  }
  .font-md-24 {
    font-size: 24px;
  }
  .font-md-26 {
    font-size: 26px;
  }
  .font-md-28 {
    font-size: 28px;
  }
  .font-md-30 {
    font-size: 30px;
  }
  .font-md-32 {
    font-size: 32px;
  }
  .font-md-34 {
    font-size: 34px;
  }
  .font-md-36 {
    font-size: 36px;
  }
  .font-md-40 {
    font-size: 40px;
  }
  .font-md-42 {
    font-size: 42px;
  }
  .font-md-44 {
    font-size: 44px;
  }
  .font-md-46 {
    font-size: 48px;
  }
  .font-md-48 {
    font-size: 48px;
  }
  .font-md-50 {
    font-size: 60px;
  }
  .font-md-52 {
    font-size: 52px;
  }
  .font-md-54 {
    font-size: 54px;
  }
  .font-md-56 {
    font-size: 56px;
  }
  .font-md-58 {
    font-size: 58px;
  }
  .font-md-60 {
    font-size: 60px;
  }
  .font-md-62 {
    font-size: 62px;
  }
  .font-md-74 {
    font-size: 74px;
  }
  .font-md-76 {
    font-size: 76px;
  }
  .font-md-78 {
    font-size: 78px;
  }
  .font-md-80 {
    font-size: 80px;
  }
  .font-md-82 {
    font-size: 82px;
  }
  .font-md-84 {
    font-size: 84px;
  }
  .font-md-86 {
    font-size: 86px;
  }
  .font-md-88 {
    font-size: 88px;
  }
  .font-md-90 {
    font-size: 90px;
  }
  .font-md-92 {
    font-size: 92px;
  }
  .font-md-94 {
    font-size: 94px;
  }
  .font-md-96 {
    font-size: 96px;
  }
  .font-md-98 {
    font-size: 98px;
  }
  .font-md-100 {
    font-size: 100px;
  }
  .line-height-md-12 {
    line-height: 12px;
  }
  .line-height-md-14 {
    line-height: 14px;
  }
  .line-height-md-16 {
    line-height: 16px;
  }
  .line-height-md-18 {
    line-height: 18px;
  }
  .line-height-md-20 {
    line-height: 20px;
  }
  .line-height-md-22 {
    line-height: 22px;
  }
  .line-height-md-24 {
    line-height: 24px;
  }
  .line-height-md-26 {
    line-height: 26px;
  }
  .line-height-md-28 {
    line-height: 28px;
  }
  .line-height-md-30 {
    line-height: 30px;
  }
  .line-height-md-32 {
    line-height: 32px;
  }
  .line-height-md-34 {
    line-height: 34px;
  }
  .line-height-md-36 {
    line-height: 36px;
  }
  .line-height-md-38 {
    line-height: 38px;
  }
  .line-height-md-40 {
    line-height: 40px;
  }
  .line-height-md-42 {
    line-height: 42px;
  }
  .line-height-md-44 {
    line-height: 44px;
  }
  .line-height-md-48 {
    line-height: 48px;
  }
  .line-height-md-50 {
    line-height: 50px;
  }
  .line-height-md-52 {
    line-height: 52px;
  }
  .line-height-md-54 {
    line-height: 54px;
  }
  .line-height-md-56 {
    line-height: 56px;
  }
  .line-height-md-58 {
    line-height: 58px;
  }
  .line-height-md-60 {
    line-height: 60px;
  }
  .line-height-md-62 {
    line-height: 62px;
  }
  .line-height-md-64 {
    line-height: 64px;
  }
  .line-height-md-66 {
    line-height: 66px;
  }
  .line-height-md-68 {
    line-height: 68px;
  }
  .line-height-md-70 {
    line-height: 70px;
  }
  .line-height-md-72 {
    line-height: 72px;
  }
  .line-height-md-74 {
    line-height: 74px;
  }
  .line-height-md-76 {
    line-height: 76px;
  }
  .line-height-md-78 {
    line-height: 78px;
  }
  .line-height-md-80 {
    line-height: 80px;
  }
  .line-height-md-82 {
    line-height: 82px;
  }
  .line-height-md-84 {
    line-height: 84px;
  }
  .line-height-md-86 {
    line-height: 86px;
  }
  .line-height-md-88 {
    line-height: 88px;
  }
  .line-height-md-90 {
    line-height: 90px;
  }
  .line-height-md-92 {
    line-height: 92px;
  }
  .line-height-md-94 {
    line-height: 94px;
  }
  .line-height-md-96 {
    line-height: 96px;
  }
  .line-height-md-98 {
    line-height: 98px;
  }
  .line-height-md-100 {
    line-height: 100px;
  }
}
@media screen and (min-width: 576px) {
  .font-sm-12 {
    font-size: 12px;
  }
  .font-sm-14 {
    font-size: 14px;
  }
  .font-sm-16 {
    font-size: 16px;
  }
  .font-sm-18 {
    font-size: 18px;
  }
  .font-sm-20 {
    font-size: 20px;
  }
  .font-sm-22 {
    font-size: 22px;
  }
  .font-sm-24 {
    font-size: 24px;
  }
  .font-sm-26 {
    font-size: 26px;
  }
  .font-sm-28 {
    font-size: 28px;
  }
  .font-sm-30 {
    font-size: 30px;
  }
  .font-sm-32 {
    font-size: 32px;
  }
  .font-sm-34 {
    font-size: 34px;
  }
  .font-sm-36 {
    font-size: 36px;
  }
  .font-sm-40 {
    font-size: 40px;
  }
  .font-sm-42 {
    font-size: 42px;
  }
  .font-sm-44 {
    font-size: 44px;
  }
  .font-sm-46 {
    font-size: 48px;
  }
  .font-sm-48 {
    font-size: 48px;
  }
  .font-sm-50 {
    font-size: 60px;
  }
  .font-sm-52 {
    font-size: 52px;
  }
  .font-sm-54 {
    font-size: 54px;
  }
  .font-sm-56 {
    font-size: 56px;
  }
  .font-sm-58 {
    font-size: 58px;
  }
  .font-sm-60 {
    font-size: 60px;
  }
  .font-sm-62 {
    font-size: 62px;
  }
  .font-sm-74 {
    font-size: 74px;
  }
  .font-sm-76 {
    font-size: 76px;
  }
  .font-sm-78 {
    font-size: 78px;
  }
  .font-sm-80 {
    font-size: 80px;
  }
  .font-sm-82 {
    font-size: 82px;
  }
  .font-sm-84 {
    font-size: 84px;
  }
  .font-sm-86 {
    font-size: 86px;
  }
  .font-sm-88 {
    font-size: 88px;
  }
  .font-sm-90 {
    font-size: 90px;
  }
  .font-sm-92 {
    font-size: 92px;
  }
  .font-sm-94 {
    font-size: 94px;
  }
  .font-sm-96 {
    font-size: 96px;
  }
  .font-sm-98 {
    font-size: 98px;
  }
  .font-sm-100 {
    font-size: 100px;
  }
  .line-height-sm-12 {
    line-height: 12px;
  }
  .line-height-sm-14 {
    line-height: 14px;
  }
  .line-height-sm-16 {
    line-height: 16px;
  }
  .line-height-sm-18 {
    line-height: 18px;
  }
  .line-height-sm-20 {
    line-height: 20px;
  }
  .line-height-sm-22 {
    line-height: 22px;
  }
  .line-height-sm-24 {
    line-height: 24px;
  }
  .line-height-sm-26 {
    line-height: 26px;
  }
  .line-height-sm-28 {
    line-height: 28px;
  }
  .line-height-sm-30 {
    line-height: 30px;
  }
  .line-height-sm-32 {
    line-height: 32px;
  }
  .line-height-sm-34 {
    line-height: 34px;
  }
  .line-height-sm-36 {
    line-height: 36px;
  }
  .line-height-sm-38 {
    line-height: 38px;
  }
  .line-height-sm-40 {
    line-height: 40px;
  }
  .line-height-sm-42 {
    line-height: 42px;
  }
  .line-height-sm-44 {
    line-height: 44px;
  }
  .line-height-sm-48 {
    line-height: 48px;
  }
  .line-height-sm-50 {
    line-height: 50px;
  }
  .line-height-sm-52 {
    line-height: 52px;
  }
  .line-height-sm-54 {
    line-height: 54px;
  }
  .line-height-sm-56 {
    line-height: 56px;
  }
  .line-height-sm-58 {
    line-height: 58px;
  }
  .line-height-sm-60 {
    line-height: 60px;
  }
  .line-height-sm-62 {
    line-height: 62px;
  }
  .line-height-sm-64 {
    line-height: 64px;
  }
  .line-height-sm-66 {
    line-height: 66px;
  }
  .line-height-sm-68 {
    line-height: 68px;
  }
  .line-height-sm-70 {
    line-height: 70px;
  }
  .line-height-sm-72 {
    line-height: 72px;
  }
  .line-height-sm-74 {
    line-height: 74px;
  }
  .line-height-sm-76 {
    line-height: 76px;
  }
  .line-height-sm-78 {
    line-height: 78px;
  }
  .line-height-sm-80 {
    line-height: 80px;
  }
  .line-height-sm-82 {
    line-height: 82px;
  }
  .line-height-sm-84 {
    line-height: 84px;
  }
  .line-height-sm-86 {
    line-height: 86px;
  }
  .line-height-sm-88 {
    line-height: 88px;
  }
  .line-height-sm-90 {
    line-height: 90px;
  }
  .line-height-sm-92 {
    line-height: 92px;
  }
  .line-height-sm-94 {
    line-height: 94px;
  }
  .line-height-sm-96 {
    line-height: 96px;
  }
  .line-height-sm-98 {
    line-height: 98px;
  }
  .line-height-sm-100 {
    line-height: 100px;
  }
}
.mr-2 {
  margin-right: 8px;
}

@media (max-width: 767px) {
  .mobile-hidden {
    display: none;
  }
}
.mb-30 {
  margin-bottom: 30px;
}

.mobile-sidebar .single-footer h3 {
  color: #ffffff;
}

.mobile-sidebar .single-contact a {
  color: #ffffff;
}

.lg-ml-15 {
  margin-left: 15px;
}

@media (max-width: 767px) {
  .lg-ml-15 {
    margin-left: 0;
  }
}
.lg-mr-15 {
  margin-right: 15px;
}

@media (max-width: 767px) {
  .lg-mr-15 {
    margin-right: 0;
  }
}
._relative {
  position: relative;
}

._absolute {
  position: absolute;
}

.bg-cover {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

.bg-contain {
  background-size: contain;
  background-position: center center;
}

.img-cover img,
.img-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.width100 img {
  width: 100%;
}

/*
 ::::::::::::::::::::::::::
  fonts area css
 ::::::::::::::::::::::::::
 */
:root {
  --vtc-text-title-1: #18305B;
  --vtc-text-title-2: #18305B;
  --vtc-text-title-3: #18305B;
  --vtc-text-title-4: #18305B;
  --vtc-text-title-5: #18305B;
  --vtc-text-title-6: #18305B;
  --vtc-text-title-7: #0A1613;
  --vtc-text-title-8: #0A1613;
  --vtc-text-sub-title-1: #18305B;
  --vtc-text-sub-title-2: #18305B;
  --vtc-text-sub-title-3: #E74526;
  --vtc-text-sub-title-4: #7246EA;
  --vtc-text-pera-1: #585858;
  --vtc-text-pera-2: #37385C;
  --vtc-text-pera-3: rgba(255, 255, 255, 0.7725490196);
  --vtc-text-pera-4: #4E4D59;
  --vtc-text-pera-5: rgba(255, 255, 255, 0.9);
  --vtc-text-pera-6: #585858;
  --vtc-text-pera-7: #414141;
  --vtc-text-pera-8: #414141;
  --vtc-text-white: #fff;
  --vtc-bg-main1: #FCDB00;
  --vtc-bg-main2: #18305B;
  --vtc-bg-main3: #18305B;
  --vtc-bg-main4: #FB8500;
  --vtc-bg-main5: #191D88;
  --vtc-bg-main6: #86B03C;
  --vtc-bg-main7: #024912;
  --vtc-bg-main8: #E74526;
  --vtc-bg-main9: #000052;
  --vtc-bg-main10: linear-gradient(90deg, #2E0797 0%, #726EFC 100%);
  --vtc-bg-main11: linear-gradient(90deg, #726EFC 0%, #2E0797 100%);
  --vtc-bg-main12: #7246EA;
  --vtc-bg-common-1: #18305B;
  --vtc-bg-common-2: #F3F5F5;
  --vtc-bg-common-3: #E7EBEB;
  --vtc-bg-common-4: rgba(255, 255, 255, 0.0901960784);
  --vtc-bg-common-5: #F4F5FF;
  --vtc-bg-common-6: #F4F5FF;
  --vtc-bg-common-7: #DEDFF3;
  --vtc-bg-common-8: #F4F5FF;
  --vtc-bg-common-9: #F2F2F2;
  --vtc-bg-common-10: rgba(2, 73, 19, 0.0901960784);
  --vtc-bg-common-11: #EFF1ED;
  --vtc-bg-common-12: #EFF1ED;
  --vtc-bg-common-13: rgba(231, 69, 38, 0.1);
  --vtc-bg-common-14: #F1F1F8;
  --vtc-bg-common-15: #F1F1F8;
  --vtc-bg-common-16: rgba(114, 70, 234, 0.1);
  --vtc-bg-common-17: #EFF1FF;
  --vtc-bg-common-18: #EFF1FF;
  --vtc-bg-common-19: #F1F1F8;
  --vtc-bg-white1: #ffffff;
  --vtc-bg-white2: #ffffff;
  --vtc-bg-white3: #ffffff;
  --vtc-bg-white4: #ffffff;
  --vtc-bg-white5: #ffffff;
  --vtc-bg-white6: #ffffff;
  --vtc-bg-white7: #ffffff;
  --vtc-bg-white8: #ffffff;
  --vtc-bg-white9: #ffffff;
  --vtc-border-1: #D1D2D5;
  --vtc-border-2: #DADAE1;
  --vtc-border-3: rgba(255, 255, 255, 0.1647058824);
  --vtc-border-4: #D1D2D5;
  --f-fw-normal: 400;
  --f-fw-medium: 500;
  --f-fw-semibold: 600;
  --f-fw-bold: 700;
  --f-fw-ex-bold: 800;
  --f-ff-font-1: 'Figtree', sans-serif;
  --f-fs-font-16: 16px;
  --f-fs-font-18: 18px;
  --f-fs-font-20: 20px;
  --f-fs-font-22: 22px;
  --f-fs-font-24: 24px;
  --f-fs-font-26: 26px;
  --f-fs-font-28: 28px;
  --f-fs-font-30: 30px;
  --f-fs-font-32: 32px;
  --f-fs-font-34: 34px;
  --f-fs-font-36: 36px;
  --f-fs-font-40: 40px;
  --f-fs-font-42: 42px;
  --f-fs-font-44: 44px;
  --f-fs-font-48: 48px;
  --f-fs-font-50: 50px;
  --f-fs-font-52: 52px;
  --f-fs-font-54: 54px;
  --f-fs-font-56: 56px;
  --f-fs-font-58: 58px;
  --f-fs-font-60: 60px;
  --f-fs-font-62: 62px;
  --f-fs-font-64: 64px;
  --f-fs-font-66: 66px;
  --f-fs-font-68: 68px;
  --f-fs-font-70: 70px;
  --f-fs-font-72: 72px;
  --f-fs-font-74: 74px;
  --f-fs-font-76: 76px;
  --f-fs-font-78: 78px;
  --f-fs-font-80: 80px;
  --f-fs-font-82: 82px;
  --f-fs-font-84: 84px;
  --f-fs-font-86: 86px;
  --f-fs-font-88: 88px;
}

/*
::::::::::::::::::::::::::
 COMMON AREA CSS
::::::::::::::::::::::::::
*/
.sub-title2 img {
  transform: translateY(-3px);
  margin-right: 3px;
}

.sub-title3 {
  padding: 8px 12px;
  background-color: var(--vtc-bg-common-10);
  border-radius: 8px;
}

.sub-title4 {
  padding: 8px 12px;
  background-color: var(--vtc-bg-common-13);
  border-radius: 8px;
  color: var(--vtc-text-sub-title-3);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  display: inline-block;
  margin-bottom: 16px;
}

.sub-title5 {
  padding: 8px 12px;
  background-color: var(--vtc-bg-common-15);
  border-radius: 8px;
  color: var(--vtc-text-sub-title-4);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  display: inline-block;
  margin-bottom: 16px;
}

.sub-title3-w {
  padding: 8px 12px;
  background-color: var(--vtc-bg-common-4);
  border-radius: 8px;
}

.dark-mode .sub-title2 img {
  transform: translateY(-3px);
  margin-right: 3px;
  filter: brightness(0) invert(1);
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .heading3 h2 {
    line-height: 38px;
  }
}
@media (max-width: 767px) {
  .heading3 h2 {
    line-height: 38px;
  }
}

/*============================
++++PAGE-PROGRESS-SATRT+++++
=============================*/
/*============================
++++PAGE-PROGRESS-END+++++
=============================*/
.progress-wrap.progress-wrap2 {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.267);
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  transition: all 200ms linear;
}

.progress-wrap.progress-wrap2.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  z-index: 99;
  color: rgb(255, 255, 255);
}

.progress-wrap.progress-wrap2::after {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  color: #f7f7f7;
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
}

.progress-wrap.progress-wrap2:hover::after {
  opacity: 0;
}

.progress-wrap.progress-wrap2::before {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  opacity: 0;
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 2;
  transition: all 200ms linear;
}

.progress-wrap.progress-wrap2:hover::before {
  opacity: 1;
}

.progress-wrap.progress-wrap2 svg path {
  fill: none;
}

.progress-wrap.progress-wrap2 svg.progress-circle path {
  stroke: #ffffff; /* --- Lijn progres kleur --- */
  stroke-width: 4;
  box-sizing: border-box;
  transition: all 200ms linear;
}

/* video button  */
.video-buttton4 {
  padding-left: 30px;
  cursor: pointer;
}
@media (max-width: 767px) {
  .video-buttton4 {
    padding-left: 0;
    padding-top: 20px;
  }
}
.video-buttton4 p {
  color: var(--vtc-text-title-6) !important;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px; /* 100% */
  margin-right: 0 !important;
}
.video-buttton4 .video-play-button {
  position: relative;
  z-index: 10;
  margin: 0px 10px;
  box-sizing: content-box;
  display: block;
  width: 32px;
  height: 44px;
  border-radius: 50%;
  padding: 18px 20px 18px 28px;
}
@media (max-width: 767px) {
  .video-buttton4 .video-play-button {
    margin: 0px 30px 0px 0px;
  }
}
.video-buttton4 .video-play-button:before {
  content: "";
  position: absolute;
  z-index: 0;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: block;
  width: 50px;
  height: 50px;
  background: var(--vtc-bg-main9);
  border-radius: 50%;
  animation: pulse-border 1500ms ease-out infinite;
  opacity: 0.4;
}
.video-buttton4 .video-play-button:after {
  content: "";
  position: absolute;
  z-index: 1;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: block;
  width: 45px;
  height: 45px;
  background: var(--vtc-bg-main9);
  border-radius: 50%;
  transition: all 200ms;
}
.video-buttton4 .video-play-button img {
  position: relative;
  z-index: 3;
  max-width: 100%;
  width: auto;
  height: auto;
}
.video-buttton4 .video-play-button span {
  display: block;
  position: relative;
  z-index: 3;
  margin-top: 14px;
  margin-left: 7px;
  width: 0;
  height: 0;
  border-left: 13px solid var(--vtc-bg-white7);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}

.details-video {
  padding-left: 30px;
  cursor: pointer;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -40px;
  margin-left: -80px;
}
@media (max-width: 767px) {
  .details-video {
    padding-left: 0;
    padding-top: 20px;
  }
}
.details-video .video-play-button {
  position: relative;
  z-index: 10;
  margin: 0px 10px;
  box-sizing: content-box;
  display: block;
  width: 32px;
  height: 44px;
  border-radius: 50%;
  padding: 18px 20px 18px 28px;
}
@media (max-width: 767px) {
  .details-video .video-play-button {
    margin: 0px 30px 0px 0px;
  }
}
.details-video .video-play-button:before {
  content: "";
  position: absolute;
  z-index: 0;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: block;
  width: 70px;
  height: 70px;
  background: var(--vtc-bg-main1);
  border-radius: 50%;
  animation: pulse-border 1500ms ease-out infinite;
  opacity: 0.4;
}
.details-video .video-play-button:after {
  content: "";
  position: absolute;
  z-index: 1;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: block;
  width: 55px;
  height: 55px;
  background: var(--vtc-bg-main1);
  border-radius: 50%;
  transition: all 200ms;
}
.details-video .video-play-button img {
  position: relative;
  z-index: 3;
  max-width: 100%;
  width: auto;
  height: auto;
}
.details-video .video-play-button span {
  display: block;
  position: relative;
  z-index: 3;
  margin-top: 14px;
  margin-left: 7px;
  width: 0;
  height: 0;
  border-left: 13px solid var(--vtc-text-sub-title-1);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}

.video-buttton5 {
  padding-left: 10px;
  cursor: pointer;
}
@media (max-width: 767px) {
  .video-buttton5 {
    padding-left: 0;
    padding-top: 20px;
  }
}
.video-buttton5 p {
  color: var(--vtc-bg-white1) !important;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px; /* 100% */
  margin-right: 0 !important;
}
.video-buttton5 .video-play-button {
  position: relative;
  z-index: 10;
  margin: 0px 10px;
  box-sizing: content-box;
  display: block;
  width: 32px;
  height: 44px;
  border-radius: 50%;
  padding: 18px 20px 18px 28px;
}
@media (max-width: 767px) {
  .video-buttton5 .video-play-button {
    margin: 0px 30px 0px 0px;
  }
}
.video-buttton5 .video-play-button:before {
  content: "";
  position: absolute;
  z-index: 0;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: block;
  width: 50px;
  height: 50px;
  background: var(--vtc-bg-white1);
  border-radius: 50%;
  animation: pulse-border 1500ms ease-out infinite;
  opacity: 0.4;
}
.video-buttton5 .video-play-button:after {
  content: "";
  position: absolute;
  z-index: 1;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: block;
  width: 45px;
  height: 45px;
  background: var(--vtc-bg-white1);
  border-radius: 50%;
  transition: all 200ms;
}
.video-buttton5 .video-play-button img {
  position: relative;
  z-index: 3;
  max-width: 100%;
  width: auto;
  height: auto;
}
.video-buttton5 .video-play-button span {
  display: block;
  position: relative;
  z-index: 3;
  margin-top: 14px;
  margin-left: 7px;
  width: 0;
  height: 0;
  border-left: 13px solid var(--vtc-text-title-8);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}

.video-area1 .video-button {
  position: absolute;
  top: 50%;
  left: 50%;
  cursor: pointer;
  margin-top: -45px;
  margin-left: -60px;
}
.video-area1 .video-play-button:before {
  content: "";
  position: absolute;
  z-index: 0;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: block;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.4117647059);
  border-radius: 50%;
  animation: pulse-border 1500ms ease-out infinite;
}
.video-area1 .video-play-button:after {
  content: "";
  position: absolute;
  z-index: 1;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  display: block;
  width: 70px;
  height: 70px;
  background: #fff;
  border-radius: 50%;
  transition: all 200ms;
}
.video-area1 .video-play-button img {
  position: relative;
  z-index: 3;
  max-width: 100%;
  width: auto;
  height: auto;
  margin-top: 5px;
}
.video-area1 .video-play-button span {
  display: block;
  position: relative;
  z-index: 3;
  margin-top: 13px;
  margin-left: 7px;
  width: 0;
  height: 0;
  border-radius: 4px;
  border-left: 15px solid #4A43DD;
  border-top: 9px solid transparent;
  border-bottom: 9px solid transparent;
}
@keyframes pulse-border {
  0% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
    opacity: 0;
  }
}

/*Pagination*/
.theme-pagination ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.theme-pagination li {
  display: inline-block;
  background: none;
}

.theme-pagination li a {
  width: 55px;
  height: 55px;
  text-align: center;
  line-height: 55px;
  background-color: var(--vtc-bg-common-2);
  margin: 0 4px;
  transition: all 0.3s;
  display: block;
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-18);
  font-weight: 700;
  border-radius: 8px;
  position: relative;
}

.recent-post-content {
  padding-right: 20px;
}

.theme-pagination li a:hover, .theme-pagination li a.active {
  background: var(--vtc-bg-main1);
  transition: all 0.3s;
  color: var(--vtc-text-title-1);
}

/*
::::::::::::::::::::::::::
 COMMON AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BUTTONS AREA CSS
::::::::::::::::::::::::::
*/
.theme-btn1 {
  display: inline-flex;
  align-items: center;
  background: none;
  border: none;
}
.theme-btn1 span.text {
  display: inline-block;
  padding: 18px 24px;
  background-color: var(--vtc-bg-main1);
  color: var(--vtc-text-title-2);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  border-radius: 111px;
  transition: all 0.4s;
}
.theme-btn1 span.arrow-all {
  display: inline-block;
  background-color: var(--vtc-bg-main1);
  color: var(--vtc-text-title-2);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  border-radius: 111px;
  height: 50px;
  width: 50px;
  text-align: center;
  line-height: 50px;
  transition: all 0.4s;
  overflow: hidden;
  transition: all 0.4s;
}
.theme-btn1 span.arrow-all span {
  display: inline-block;
  transform: rotate(-45deg) translateX(6px) translateY(7px);
  font-size: var(--f-fs-font-18);
  transition: all 0.4s;
  background: none;
}
.theme-btn1 span.arrow-all .arrow2 {
  transform: translateY(-8px) rotate(-45deg) translateX(-52px);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn1 span.arrow-all .arrow1 {
  transition: all 0.4s;
  opacity: 1;
}
.theme-btn1:hover span {
  transition: all 0.4s;
  background-color: #d6b64c;
}
.theme-btn1:hover .arrow-all {
  transform: translateX(3px);
  transition: all 0.4s;
}
.theme-btn1:hover .arrow-all .arrow2 {
  transform: translateY(-7px) rotate(-45deg) translateX(-10px);
  transition: all 0.4s;
  opacity: 1;
}
.theme-btn1:hover .arrow-all .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.theme-btn2 {
  display: inline-flex;
  align-items: center;
  background: none;
  border: none;
}
.theme-btn2 span.text {
  display: inline-block;
  padding: 18px 24px;
  background-color: var(--vtc-bg-main4);
  color: var(--vtc-text-white);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  border-radius: 111px;
  transition: all 0.4s;
}
.theme-btn2 span.arrow-all {
  display: inline-block;
  background-color: var(--vtc-bg-main4);
  color: var(--vtc-text-white);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  border-radius: 111px;
  height: 50px;
  width: 50px;
  text-align: center;
  line-height: 50px;
  transition: all 0.4s;
  overflow: hidden;
  transition: all 0.4s;
}
.theme-btn2 span.arrow-all span {
  display: inline-block;
  transform: rotate(-45deg) translateX(6px) translateY(7px);
  font-size: var(--f-fs-font-18);
  transition: all 0.4s;
  background: none;
}
.theme-btn2 span.arrow-all .arrow2 {
  transform: translateY(-8px) rotate(-45deg) translateX(-52px);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn2 span.arrow-all .arrow1 {
  transition: all 0.4s;
  opacity: 1;
}
.theme-btn2:hover span {
  transition: all 0.4s;
  background-color: #fc9d32;
}
.theme-btn2:hover .arrow-all {
  transform: translateX(3px);
  transition: all 0.4s;
}
.theme-btn2:hover .arrow-all .arrow2 {
  transform: translateY(-7px) rotate(-45deg) translateX(-10px);
  transition: all 0.4s;
  opacity: 1;
}
.theme-btn2:hover .arrow-all .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.theme-btn3 {
  display: inline-block;
  padding: 19px 22px;
  border-radius: 8px;
  color: var(--vtc-bg-white1) !important;
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  transition: all 0.4s;
  position: relative;
  z-index: 3;
  overflow: hidden;
  transition: all 0.4s;
}
.theme-btn3 .text {
  position: relative;
  display: inline-block;
}
.theme-btn3 .text::after {
  content: "";
  position: absolute;
  left: -12px;
  top: -9px;
  height: 36px;
  width: 36px;
  background-color: rgba(255, 255, 255, 0.1921568627);
  border-radius: 50%;
}
.theme-btn3::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  background-color: var(--vtc-bg-main6);
  z-index: -2;
}
.theme-btn3::before {
  content: "";
  position: absolute;
  top: 0;
  right: -2px;
  height: 100%;
  width: 103%;
  background-color: var(--vtc-bg-main7);
  z-index: -1;
  transform: rotateY(75deg);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn3:hover {
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.theme-btn3:hover::before {
  opacity: 1;
  transition: all 0.4s;
  transform: rotateY(0deg);
}

.theme-btn4 {
  display: inline-block;
  padding: 19px 22px;
  border-radius: 8px;
  color: var(--vtc-text-title-2);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  transition: all 0.4s;
  position: relative;
  z-index: 3;
  overflow: hidden;
  transition: all 0.4s;
}
.theme-btn4 .text {
  position: relative;
  display: inline-block;
}
.theme-btn4 .text::after {
  content: "";
  position: absolute;
  left: -12px;
  top: -9px;
  height: 36px;
  width: 36px;
  background-color: rgba(0, 0, 0, 0.1921568627);
  border-radius: 50%;
}
.theme-btn4::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  background-color: var(--vtc-bg-white1);
  z-index: -2;
}
.theme-btn4::before {
  content: "";
  position: absolute;
  top: 0;
  right: -2px;
  height: 100%;
  width: 103%;
  background-color: var(--vtc-bg-main7);
  z-index: -1;
  transform: rotateY(75deg);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn4:hover {
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.theme-btn4:hover::before {
  opacity: 1;
  transition: all 0.4s;
  transform: rotateY(0deg);
}

.theme-btn5 {
  display: inline-flex;
  padding: 19px 22px;
  border-radius: 8px;
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  transition: all 0.4s;
  position: relative;
  z-index: 3;
  overflow: hidden;
  transition: all 0.4s;
  border: none;
}
.theme-btn5 span.arrow-all {
  display: inline-block;
  transform: translateX(0px) translateY(-2px);
  font-size: 17px;
  transition: all 0.4s;
  width: 25px;
  height: 18px;
}
.theme-btn5 .arrow2 {
  display: inline-block;
  transform: translateY(-20px) translateX(-20px);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn5 .arrow1 {
  transition: all 0.4s;
  opacity: 1;
  display: inline-block;
}
.theme-btn5:hover .arrow2 {
  transition: all 0.4s;
  opacity: 1;
  display: inline-block;
  transform: translateY(-20px) translateX(0px);
}
.theme-btn5:hover .arrow1 {
  display: inline-block;
  transition: all 0.4s;
  opacity: 0;
  transform: translateX(30px);
}
.theme-btn5 .text {
  position: relative;
  display: inline-block;
}
.theme-btn5 .text::after {
  content: "";
  position: absolute;
  left: -12px;
  top: -9px;
  height: 36px;
  width: 36px;
  background-color: rgba(255, 255, 255, 0.1921568627);
  border-radius: 50%;
  z-index: -1;
}
.theme-btn5::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  background-color: var(--vtc-bg-main8);
  z-index: -2;
}
.theme-btn5::before {
  content: "";
  position: absolute;
  top: 0;
  right: -2px;
  height: 100%;
  width: 103%;
  background-color: #f15b3d;
  z-index: -1;
  transform: rotateY(75deg);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn5:hover {
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.theme-btn5:hover::before {
  opacity: 1;
  transition: all 0.4s;
  transform: rotateY(0deg);
}

.theme-btn6 {
  display: inline-flex;
  padding: 19px 18px 19px 22px;
  border-radius: 8px;
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  transition: all 0.4s;
  position: relative;
  z-index: 3;
  overflow: hidden;
  transition: all 0.4s;
  border: none;
}
.theme-btn6 span.arrow-all {
  display: inline-block;
  transform: translateX(3px) translateY(-3px) rotate(-45deg);
  font-size: 17px;
  transition: all 0.4s;
  width: 25px;
  height: 18px;
}
.theme-btn6 .arrow2 {
  display: inline-block;
  transform: translateY(-20px) translateX(-20px);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn6 .arrow1 {
  transition: all 0.4s;
  opacity: 1;
  display: inline-block;
}
.theme-btn6:hover .arrow2 {
  transition: all 0.4s;
  opacity: 1;
  display: inline-block;
  transform: translateY(-20px) translateX(0px);
}
.theme-btn6:hover .arrow1 {
  display: inline-block;
  transition: all 0.4s;
  opacity: 0;
  transform: translateX(30px);
}
.theme-btn6 .text {
  position: relative;
  display: inline-block;
}
.theme-btn6::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  background: var(--vtc-bg-main10);
  z-index: -2;
}
.theme-btn6::before {
  content: "";
  position: absolute;
  top: 0;
  right: -2px;
  height: 100%;
  width: 103%;
  background: var(--vtc-bg-main11);
  z-index: -1;
  transform: rotateY(75deg);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn6:hover {
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.theme-btn6:hover::before {
  opacity: 1;
  transition: all 0.4s;
  transform: rotateY(0deg);
}

.theme-btn7 {
  display: inline-flex;
  padding: 19px 18px 19px 22px;
  border-radius: 8px;
  color: var(--vtc-text-title-8);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  transition: all 0.4s;
  position: relative;
  z-index: 3;
  overflow: hidden;
  transition: all 0.4s;
  border: none;
}
.theme-btn7 span.arrow-all {
  display: inline-block;
  transform: translateX(3px) translateY(-3px) rotate(-45deg);
  font-size: 17px;
  transition: all 0.4s;
  width: 25px;
  height: 18px;
}
.theme-btn7 .arrow2 {
  display: inline-block;
  transform: translateY(-20px) translateX(-20px);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn7 .arrow1 {
  transition: all 0.4s;
  opacity: 1;
  display: inline-block;
}
.theme-btn7:hover .arrow2 {
  transition: all 0.4s;
  opacity: 1;
  display: inline-block;
  transform: translateY(-20px) translateX(0px);
}
.theme-btn7:hover .arrow1 {
  display: inline-block;
  transition: all 0.4s;
  opacity: 0;
  transform: translateX(30px);
}
.theme-btn7 .text {
  position: relative;
  display: inline-block;
}
.theme-btn7::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  background: var(--vtc-bg-white1);
  z-index: -2;
}
.theme-btn7::before {
  content: "";
  position: absolute;
  top: 0;
  right: -2px;
  height: 100%;
  width: 103%;
  background: var(--vtc-bg-main11);
  z-index: -1;
  transform: rotateY(75deg);
  transition: all 0.4s;
  opacity: 0;
}
.theme-btn7:hover {
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
  transform: translateY(-5px);
}
.theme-btn7:hover::before {
  opacity: 1;
  transition: all 0.4s;
  transform: rotateY(0deg);
}

.learn1 {
  display: inline-block;
  overflow: hidden;
}
.learn1 span {
  display: inline-block;
  transform: rotate(-45deg) translateX(0px) translateY(1px);
  font-size: 17px;
  transition: all 0.4s;
}
.learn1 .arrow2 {
  transform: translateY(-8px) rotate(-45deg) translateX(-52px);
  transition: all 0.4s;
  opacity: 0;
}
.learn1 .arrow1 {
  transition: all 0.4s;
  opacity: 1;
}
.learn1:hover {
  color: #4A43DD;
}
.learn1:hover .arrow2 {
  transform: translateY(-12px) rotate(-45deg) translateX(-18px);
  transition: all 0.4s;
  opacity: 1;
}
.learn1:hover .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.learn2 {
  display: inline-flex;
  align-items: center;
  background: none;
  border: none;
}
.learn2 span.arrow-all {
  display: inline-block;
  background-color: var(--vtc-bg-main6);
  color: var(--vtc-text-white);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  border-radius: 111px;
  height: 50px;
  width: 50px;
  text-align: center;
  line-height: 50px;
  transition: all 0.4s;
  overflow: hidden;
  transition: all 0.4s;
}
.learn2 span.arrow-all span {
  display: inline-block;
  transform: rotate(-45deg) translateX(6px) translateY(6px);
  font-size: var(--f-fs-font-18);
  transition: all 0.4s;
  background: none;
}
.learn2 span.arrow-all .arrow2 {
  transform: translateY(-8px) rotate(-45deg) translateX(-52px);
  transition: all 0.4s;
  opacity: 0;
}
.learn2 span.arrow-all .arrow1 {
  transition: all 0.4s;
  opacity: 1;
}
.learn2:hover span {
  transition: all 0.4s;
  background-color: #a7d35a;
}
.learn2:hover .arrow-all {
  transform: translateX(3px);
  transition: all 0.4s;
}
.learn2:hover .arrow-all .arrow2 {
  transform: translateY(-7px) rotate(-45deg) translateX(-10px);
  transition: all 0.4s;
  opacity: 1;
}
.learn2:hover .arrow-all .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.learn3 {
  display: inline-flex;
  align-items: center;
  background: none;
  border: none;
}
.learn3 span.arrow-all {
  display: inline-block;
  background-color: var(--vtc-bg-main4);
  color: var(--vtc-text-white);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: uppercase;
  border-radius: 111px;
  height: 50px;
  width: 50px;
  text-align: center;
  line-height: 50px;
  transition: all 0.4s;
  overflow: hidden;
  transition: all 0.4s;
}
.learn3 span.arrow-all span {
  display: inline-block;
  transform: rotate(-45deg) translateX(6px) translateY(6px);
  font-size: var(--f-fs-font-18);
  transition: all 0.4s;
  background: none;
}
.learn3 span.arrow-all .arrow2 {
  transform: translateY(-8px) rotate(-45deg) translateX(-52px);
  transition: all 0.4s;
  opacity: 0;
}
.learn3 span.arrow-all .arrow1 {
  transition: all 0.4s;
  opacity: 1;
}
.learn3:hover span {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main4);
}
.learn3:hover .arrow-all {
  transform: translateX(3px);
  transition: all 0.4s;
}
.learn3:hover .arrow-all .arrow2 {
  transform: translateY(-7px) rotate(-45deg) translateX(-10px);
  transition: all 0.4s;
  opacity: 1;
}
.learn3:hover .arrow-all .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

/*
::::::::::::::::::::::::::
    BUTOTNS AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 CTA AREA CSS
::::::::::::::::::::::::::
*/
.cta1-form {
  margin-left: 120px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta1-form {
    margin-left: 0;
    margin-top: 30px;
    margin-right: 100px;
  }
}
@media (max-width: 767px) {
  .cta1-form {
    margin-left: 0;
    margin-top: 30px;
  }
}
.cta1-form input {
  width: 100%;
  padding: 20px;
  border: none;
  border-radius: 111px;
  background-color: #fff;
}
.cta1-form input:focus {
  outline: none;
}
.cta1-form input::-moz-placeholder {
  color: var(--Home-Page-4-Color-Text-Color, #032530);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-20); /* 100% */
}
.cta1-form input::placeholder {
  color: var(--Home-Page-4-Color-Text-Color, #032530);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-20); /* 100% */
}
.cta1-form button {
  position: absolute;
  right: 5px;
  top: 5px;
}

.cta1 {
  margin-bottom: -150px;
  overflow: hidden;
}
.cta1 .bg-cover {
  margin-left: -60px;
  margin-right: -60px;
  padding: 76px 60px;
}
.cta1 .shape {
  position: absolute;
  bottom: 0;
  right: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta1 .shape {
    display: none;
  }
}
@media (max-width: 767px) {
  .cta1 .shape {
    display: none;
  }
}

.cta2 {
  padding: 80px 0px;
}

.cta2-form {
  margin-left: 120px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta2-form {
    margin-left: 0;
    margin-top: 30px;
    margin-right: 100px;
  }
}
@media (max-width: 767px) {
  .cta2-form {
    margin-left: 0;
    margin-top: 30px;
  }
}
.cta2-form input {
  width: 100%;
  padding: 20px;
  border: none;
  border-radius: 111px;
  background-color: #fff;
}
.cta2-form input:focus {
  outline: none;
}
.cta2-form input::-moz-placeholder {
  color: var(--Home-Page-4-Color-Text-Color, #032530);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-20); /* 100% */
}
.cta2-form input::placeholder {
  color: var(--Home-Page-4-Color-Text-Color, #032530);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-20); /* 100% */
}
.cta2-form button {
  position: absolute;
  right: 5px;
  top: 5px;
}

.cta2 {
  margin-bottom: -150px;
  overflow: hidden;
}
.cta2 .bg-cover {
  margin-left: -60px;
  margin-right: -60px;
  padding: 76px 60px;
}
.cta2 .shape {
  position: absolute;
  bottom: -80px;
  right: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta2 .shape {
    display: none;
  }
}
@media (max-width: 767px) {
  .cta2 .shape {
    display: none;
  }
}

.cta3 {
  overflow: hidden;
}
.cta3 .heading-w {
  padding: 60px 0px;
}
.cta3 .images {
  position: relative;
  height: 336px;
  text-align: end;
}
.cta3 .images .image {
  position: relative;
  z-index: 3;
  margin-right: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta3 .images .image {
    margin-bottom: 20px;
  }
}
.cta3 .images .shape1 {
  position: absolute;
  bottom: -15px;
  right: 0;
}
.cta3 .images .shape2 {
  position: absolute;
  top: 40px;
  left: 133px;
}
@media (max-width: 767px) {
  .cta3 .images .shape2 {
    left: 0;
  }
}
.cta3 .images .shape3 {
  position: absolute;
  top: 0;
  right: 0;
}

.cta4-bg {
  padding: 80px 0px;
  border-bottom: 1px solid var(--vtc-border-2);
}

.cta4-form {
  margin-left: 120px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta4-form {
    margin-left: 0;
    margin-top: 30px;
    margin-right: 100px;
  }
}
@media (max-width: 767px) {
  .cta4-form {
    margin-left: 0;
    margin-top: 30px;
  }
}
.cta4-form input {
  width: 100%;
  padding: 21px;
  border: none;
  border-radius: 8px;
  background-color: var(--vtc-bg-common-14);
}
.cta4-form input:focus {
  outline: none;
}
.cta4-form input::-moz-placeholder {
  color: var(--vtc-text-title-5);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-20); /* 100% */
  opacity: 0.7;
}
.cta4-form input::placeholder {
  color: var(--vtc-text-title-5);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-20); /* 100% */
  opacity: 0.7;
}
.cta4-form button {
  position: absolute;
  right: 5px;
  top: 5px;
}

.cta5-bg {
  padding: 80px 0px 48px 0px;
  border-bottom: 1px solid var(--vtc-border-3);
  margin-bottom: 48px;
}

.cta5-form {
  margin-left: 120px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .cta5-form {
    margin-left: 0;
    margin-top: 30px;
    margin-right: 100px;
  }
}
@media (max-width: 767px) {
  .cta5-form {
    margin-left: 0;
    margin-top: 30px;
  }
}
.cta5-form input {
  width: 100%;
  padding: 21px;
  border: none;
  border-radius: 8px;
  background-color: var(--vtc-bg-common-14);
}
.cta5-form input:focus {
  outline: none;
}
.cta5-form input::-moz-placeholder {
  color: var(--vtc-text-title-5);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-20); /* 100% */
  opacity: 0.7;
}
.cta5-form input::placeholder {
  color: var(--vtc-text-title-5);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-20); /* 100% */
  opacity: 0.7;
}
.cta5-form button {
  position: absolute;
  right: 5px;
  top: 5px;
}

/*
 ::::::::::::::::::::::::::
  CTA AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 HERO AREA CSS
::::::::::::::::::::::::::
*/
.hero1 .sub-title img {
  transform: translateY(-3px);
  margin-right: 3px;
}
.hero1 .hero1-sliders .hero1-single-slider {
  display: flex;
  align-items: center;
  min-height: 712px;
  margin-top: 86px;
  overflow: hidden;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero1 .hero1-sliders .hero1-single-slider {
    height: 900px;
  }
}
@media (max-width: 767px) {
  .hero1 .hero1-sliders .hero1-single-slider {
    height: 880px;
  }
}
.hero1 .hero1-sliders .hero1-single-slider .heading {
  overflow: hidden;
}
@media (max-width: 767px) {
  .hero1 .hero1-sliders .hero1-single-slider .heading {
    padding-top: 80px;
  }
}
.hero1 .hero1-sliders .hero1-single-slider .heading span.sub-title {
  display: inline-block;
  transition: all 1.5s;
  transform: translateX(-120px);
  opacity: 0.2;
}
.hero1 .hero1-sliders .hero1-single-slider .heading h1 {
  display: inline-block;
  transition: all 1.9s;
  transform: translateX(-180px);
  opacity: 0.2;
}
.hero1 .hero1-sliders .hero1-single-slider .heading p {
  display: inline-block;
  transition: all 2.2s;
  transform: translateX(-220px);
  opacity: 0.2;
}
.hero1 .hero1-sliders .hero1-single-slider .heading .button {
  transition: all 2.6s;
  transform: translateX(-120px);
  opacity: 0.2;
}
.hero1 .hero1-sliders .hero1-single-slider .main-image {
  position: absolute;
  right: 0;
  transform: scale(0.8) translateX(180px);
  z-index: 2;
  transition: all 2s ease-in-out;
  opacity: 0.9;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero1 .hero1-sliders .hero1-single-slider .main-image {
    bottom: -70px;
  }
}
@media (max-width: 767px) {
  .hero1 .hero1-sliders .hero1-single-slider .main-image {
    bottom: -70px;
  }
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .hero1 .hero1-sliders .hero1-single-slider .main-image {
    transform: scale(0.8) translateX(280px);
  }
}
.hero1 .hero1-sliders .hero1-single-slider .main-image-shape {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 3;
  transform: translateY(200px) translateX(200px);
  transition: all 2s ease-in-out;
  opacity: 1;
}
.hero1 .hero1-sliders .hero1-single-slider .all-images {
  height: 650px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero1 .hero1-sliders .hero1-single-slider .all-images {
    height: 500px;
  }
}
@media (max-width: 767px) {
  .hero1 .hero1-sliders .hero1-single-slider .all-images {
    height: 500px;
  }
}
.hero1 .hero1-sliders .hero1-single-slider .all-images .circle-area {
  position: absolute;
  z-index: 9;
  width: 200px;
  height: 200px;
  bottom: 100px;
  left: 100px;
  transform: translateY(100px);
  opacity: 0;
  transition: all 1.5s;
}
@media (max-width: 767px) {
  .hero1 .hero1-sliders .hero1-single-slider .all-images .circle-area {
    left: 0;
    top: 55px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero1 .hero1-sliders .hero1-single-slider .all-images .circle-area {
    left: 180px;
    top: 105px;
  }
}
.hero1 .hero1-sliders .hero1-single-slider .all-images .circle-area .circle-arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -23px;
  margin-left: -22px;
  z-index: 9;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .hero1 .hero1-sliders .hero1-single-slider .all-images .circle-area {
    left: -60px;
  }
}
.hero1 .hero1-sliders .hero1-single-slider.slick-current.slick-active .all-images .circle-area {
  transition: all 1.5s;
  transform: translateY(0);
  opacity: 1;
}
.hero1 .hero1-sliders .hero1-single-slider.slick-current.slick-active .main-image-shape {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 3;
  transform: translateY(0) translateX(0);
  transition: all 2s ease-in-out;
}
.hero1 .hero1-sliders .hero1-single-slider.slick-current.slick-active .main-image {
  position: absolute;
  right: 0;
  transform: scale(0.8) translateX(180px);
  z-index: 2;
  transition: all 1.3s ease-in-out;
  opacity: 1;
}
@media (max-width: 767px) {
  .hero1 .hero1-sliders .hero1-single-slider.slick-current.slick-active .main-image {
    width: 600px;
  }
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .hero1 .hero1-sliders .hero1-single-slider.slick-current.slick-active .main-image {
    transform: scale(0.8) translateX(280px);
  }
}
.hero1 .hero1-sliders .hero1-single-slider.slick-current.slick-active .heading {
  overflow: hidden;
}
.hero1 .hero1-sliders .hero1-single-slider.slick-current.slick-active .heading span.sub-title {
  display: inline-block;
  transition: all 1.5s;
  transform: translateX(0);
  opacity: 11;
}
.hero1 .hero1-sliders .hero1-single-slider.slick-current.slick-active .heading h1 {
  display: inline-block;
  transition: all 1.3s ease-in-out;
  transform: translateX(0);
  opacity: 1;
}
.hero1 .hero1-sliders .hero1-single-slider.slick-current.slick-active .heading p {
  display: inline-block;
  transition: all 1.4s ease-in-out;
  transform: translateX(0);
  opacity: 1;
}
.hero1 .hero1-sliders .hero1-single-slider.slick-current.slick-active .heading .button {
  transition: all 1.9s ease-in-out;
  transform: translateX(0);
  opacity: 1;
}
.hero1 .hero1-arrow-buttons {
  position: absolute;
  bottom: 32px;
  display: grid;
  right: 32px;
}
.hero1 .hero1-arrow-buttons button {
  border: none;
  border-radius: 50%;
  background: none;
  font-size: 20px;
  height: 56px;
  width: 56px;
  text-align: center;
  line-height: 56px;
  background: var(--vtc-bg-white1);
  margin: 5px 0px;
  transition: all 0.4s;
}
.hero1 .hero1-arrow-buttons button:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main1);
}

.hero2 .sub-title img {
  transform: translateY(-3px);
  margin-right: 3px;
}
.hero2 .hero2-sliders .hero2-single-slider {
  display: flex;
  align-items: center;
  min-height: 792px;
  overflow: hidden;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero2 .hero2-sliders .hero2-single-slider {
    height: 800px;
  }
}
@media (max-width: 767px) {
  .hero2 .hero2-sliders .hero2-single-slider {
    height: 880px;
  }
}
.hero2 .hero2-sliders .hero2-single-slider .heading {
  overflow: hidden;
  padding-top: 138px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero2 .hero2-sliders .hero2-single-slider .heading {
    padding-top: 100px;
  }
}
@media (max-width: 767px) {
  .hero2 .hero2-sliders .hero2-single-slider .heading {
    padding-top: 80px;
  }
}
.hero2 .hero2-sliders .hero2-single-slider .heading span.sub-title {
  display: inline-flex !important;
  transition: all 1.5s;
  transform: translateX(-120px);
  opacity: 0.2;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  padding: 10px 14px 6px 14px;
}
.hero2 .hero2-sliders .hero2-single-slider .heading h1 {
  display: inline-block;
  transition: all 1.9s;
  transform: translateX(-180px);
  opacity: 0.2;
}
.hero2 .hero2-sliders .hero2-single-slider .heading p {
  display: inline-block;
  transition: all 2.2s;
  transform: translateX(-220px);
  opacity: 0.2;
}
.hero2 .hero2-sliders .hero2-single-slider .heading .button {
  transition: all 2.6s;
  transform: translateX(-120px);
  opacity: 0.2;
}
.hero2 .hero2-sliders .hero2-single-slider .main-image {
  position: absolute;
  right: 0;
  transform: scale(0.8) translateX(180px);
  z-index: 2;
  transition: all 2s ease-in-out;
  opacity: 0.9;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero2 .hero2-sliders .hero2-single-slider .main-image {
    bottom: -70px;
  }
}
@media (max-width: 767px) {
  .hero2 .hero2-sliders .hero2-single-slider .main-image {
    bottom: -70px;
  }
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .hero2 .hero2-sliders .hero2-single-slider .main-image {
    transform: scale(0.8) translateX(280px);
  }
}
.hero2 .hero2-sliders .hero2-single-slider .main-image-shape {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 3;
  transform: translateY(200px) translateX(200px);
  transition: all 2s ease-in-out;
  opacity: 1;
}
.hero2 .hero2-sliders .hero2-single-slider .all-images {
  height: 650px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero2 .hero2-sliders .hero2-single-slider .all-images {
    height: 500px;
  }
}
@media (max-width: 767px) {
  .hero2 .hero2-sliders .hero2-single-slider .all-images {
    height: 500px;
  }
}
.hero2 .hero2-sliders .hero2-single-slider .all-images .circle-area {
  position: absolute;
  z-index: 9;
  width: 200px;
  height: 200px;
  bottom: 100px;
  left: 100px;
  transform: translateY(100px);
  opacity: 0;
  transition: all 1.5s;
}
@media (max-width: 767px) {
  .hero2 .hero2-sliders .hero2-single-slider .all-images .circle-area {
    left: 0;
    top: 55px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero2 .hero2-sliders .hero2-single-slider .all-images .circle-area {
    left: 180px;
    top: 105px;
  }
}
.hero2 .hero2-sliders .hero2-single-slider .all-images .circle-area .circle-arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -23px;
  margin-left: -22px;
  z-index: 9;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .hero2 .hero2-sliders .hero2-single-slider .all-images .circle-area {
    left: -60px;
  }
}
.hero2 .hero2-sliders .hero2-single-slider.slick-current.slick-active .all-images .circle-area {
  transition: all 1.5s;
  transform: translateY(0);
  opacity: 1;
}
.hero2 .hero2-sliders .hero2-single-slider.slick-current.slick-active .main-image-shape {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 3;
  transform: translateY(0) translateX(0);
  transition: all 2s ease-in-out;
}
.hero2 .hero2-sliders .hero2-single-slider.slick-current.slick-active .main-image {
  position: absolute;
  right: 0;
  transform: scale(0.8) translateX(180px);
  z-index: 2;
  transition: all 1.3s ease-in-out;
  opacity: 1;
}
@media (max-width: 767px) {
  .hero2 .hero2-sliders .hero2-single-slider.slick-current.slick-active .main-image {
    width: 600px;
  }
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .hero2 .hero2-sliders .hero2-single-slider.slick-current.slick-active .main-image {
    transform: scale(0.8) translateX(280px);
  }
}
.hero2 .hero2-sliders .hero2-single-slider.slick-current.slick-active .heading {
  overflow: hidden;
}
.hero2 .hero2-sliders .hero2-single-slider.slick-current.slick-active .heading span.sub-title {
  display: inline-block;
  transition: all 1.5s;
  transform: translateX(0);
  opacity: 11;
}
.hero2 .hero2-sliders .hero2-single-slider.slick-current.slick-active .heading h1 {
  display: inline-block;
  transition: all 1.3s ease-in-out;
  transform: translateX(0);
  opacity: 1;
}
.hero2 .hero2-sliders .hero2-single-slider.slick-current.slick-active .heading p {
  display: inline-block;
  transition: all 1.4s ease-in-out;
  transform: translateX(0);
  opacity: 1;
}
.hero2 .hero2-sliders .hero2-single-slider.slick-current.slick-active .heading .button {
  transition: all 1.9s ease-in-out;
  transform: translateX(0);
  opacity: 1;
}
.hero2 .hero2-arrow-buttons {
  position: absolute;
  bottom: 32px;
  display: grid;
  right: 32px;
}
@media (max-width: 767px) {
  .hero2 .hero2-arrow-buttons {
    bottom: 10px;
  }
}
.hero2 .hero2-arrow-buttons button {
  border: none;
  border-radius: 50%;
  background: none;
  font-size: 20px;
  height: 56px;
  width: 56px;
  text-align: center;
  line-height: 56px;
  background: var(--vtc-bg-white1);
  margin: 5px 0px;
  transition: all 0.4s;
}
.hero2 .hero2-arrow-buttons button:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main4);
  color: var(--vtc-bg-white1);
}

.hero3 {
  min-height: 732px;
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero3 {
    min-height: 1100px;
  }
}
.hero3 .main-heading {
  padding-top: 110px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero3 .main-heading {
    padding-top: 100px;
  }
}
@media (max-width: 767px) {
  .hero3 .main-heading {
    padding-top: 180px;
  }
}
.hero3 .main-heading span.sub-title {
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  display: inline-flex;
}
.hero3 .hero3-images {
  height: 550px;
  margin-top: 100px;
  margin-bottom: -82px;
  position: relative;
  z-index: 4;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero3 .hero3-images {
    margin-top: 40px;
    margin-bottom: -100px;
  }
}
@media (max-width: 767px) {
  .hero3 .hero3-images {
    margin-top: 40px;
    margin-bottom: 0;
  }
}
.hero3 .hero3-images .main-image {
  position: absolute;
  bottom: 0;
  right: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero3 .hero3-images .main-image {
    right: 100px;
  }
}
.hero3 .hero3-images .shape1 {
  position: absolute;
  right: -100px;
  top: 110px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero3 .hero3-images .shape1 {
    right: 0;
  }
}
@media (max-width: 767px) {
  .hero3 .hero3-images .shape1 {
    right: 0;
    width: 135px;
  }
}
.hero3 .hero3-images .shape2 {
  position: absolute;
  bottom: 50px;
  right: -120px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero3 .hero3-images .shape2 {
    right: 0;
  }
}
@media (max-width: 767px) {
  .hero3 .hero3-images .shape2 {
    right: 0;
    width: 200px;
  }
}
.hero3 .hero3-images .shape3 {
  position: absolute;
  bottom: 120px;
  left: 30px;
}
@media (max-width: 767px) {
  .hero3 .hero3-images .shape3 {
    left: 30px;
    width: 200px;
    bottom: 180px;
  }
}
.hero3 .hero3-images .shape4 {
  position: absolute;
  left: 70px;
  top: 60px;
}
@media (max-width: 767px) {
  .hero3 .hero3-images .shape4 {
    left: 0;
    top: 18px;
  }
}
.hero3 .hero3-images .main-image-bg {
  position: absolute;
  right: -40px;
  bottom: 0;
  z-index: -1;
}

.hero4 {
  position: relative;
  min-height: 770px;
  display: flex;
  align-items: center;
}
.hero4::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-image: url(../img/bg/hero4-bg1.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  z-index: -2;
}
.hero4 .main-heading {
  padding-top: 145px;
}
.hero4 .main-heading .sub-title {
  background-color: var(--vtc-bg-white1);
  padding: 6px 16px 6px 6px;
  border-radius: 30px;
  display: inline-block;
  color: var(--Home-Page-4-Color-Text-Color, #0B0916);
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 18px; /* 100% */
  margin-bottom: 16px;
}
.hero4 .main-heading .sub-title img {
  margin-right: 3px;
}
.hero4 .main-heading h1 {
  color: var(--vtc-text-title-6);
}
.hero4 .main-heading p {
  color: var(--vtc-text-pera-7);
  margin-right: 100px;
}
@media (max-width: 767px) {
  .hero4 .main-heading p {
    margin-right: 0;
  }
}
.hero4 .main-heading .buttons {
  display: flex;
  align-items: center;
}
@media (max-width: 767px) {
  .hero4 .main-heading .buttons {
    display: block;
  }
}
.hero4 .main-heading .video-buttton4 {
  display: flex;
  align-items: center;
}
.hero4 .images {
  height: 533px;
  position: relative;
  margin-top: 145px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero4 .images {
    margin-top: 50px;
    margin-bottom: 50px;
  }
}
@media (max-width: 767px) {
  .hero4 .images {
    margin-top: 50px;
    margin-bottom: 50px;
  }
}
.hero4 .images .image1 {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 2;
}
.hero4 .images .image2 {
  position: absolute;
  top: 0;
  left: 80px;
  z-index: 1;
}
@media (max-width: 767px) {
  .hero4 .images .image2 {
    left: 0;
  }
}
.hero4 .images .shape1 {
  position: absolute;
  right: 30px;
  top: 0;
}
.hero4 .images .shape2 {
  position: absolute;
  bottom: 0;
  left: 20px;
}

.dark-mode .hero4 {
  position: relative;
  min-height: 770px;
  display: flex;
  align-items: center;
}
.dark-mode .hero4::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-image: url(../img/bg/hero4-bg2.png);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  z-index: -2;
}
.dark-mode .hero4 .images .shape2 img {
  filter: brightness(0) invert(1);
}

.hero5 {
  min-height: 732px;
  display: flex;
  align-items: center;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero5 {
    min-height: 1100px;
  }
}
.hero5 .main-heading {
  padding-top: 110px;
  position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero5 .main-heading {
    padding-top: 100px;
  }
}
@media (max-width: 767px) {
  .hero5 .main-heading {
    padding-top: 180px;
  }
}
.hero5 .main-heading span.sub-title {
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px 6px 12px;
  display: inline-flex;
}
.hero5 .main-heading span.sub-title img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.hero5 .main-heading .text-shape {
  position: absolute;
  left: -90px;
  top: 50px;
}
.hero5 .main-heading .buttons {
  display: flex;
  align-items: center;
}
@media (max-width: 767px) {
  .hero5 .main-heading .buttons {
    display: block;
  }
}
.hero5 .main-heading .video-buttton5 {
  display: flex;
  align-items: center;
}
.hero5 .hero3-images {
  height: 550px;
  margin-top: 100px;
  margin-bottom: -82px;
  position: relative;
  z-index: 4;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero5 .hero3-images {
    margin-top: 40px;
    margin-bottom: -100px;
  }
}
@media (max-width: 767px) {
  .hero5 .hero3-images {
    margin-top: 40px;
    margin-bottom: 0;
    height: 400px;
  }
}
.hero5 .hero3-images .main-image {
  position: absolute;
  bottom: 0;
  right: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero5 .hero3-images .main-image {
    right: 100px;
  }
}
.hero5 .hero3-images .shape1 {
  position: absolute;
  left: 0;
  bottom: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero5 .hero3-images .shape1 {
    right: 0;
  }
}
@media (max-width: 767px) {
  .hero5 .hero3-images .shape1 {
    right: 0;
    width: 135px;
  }
}
.hero5 .hero3-images .shape2 {
  position: absolute;
  bottom: 0;
  right: -120px;
  z-index: -2;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .hero5 .hero3-images .shape2 {
    right: 0;
  }
}
@media (max-width: 767px) {
  .hero5 .hero3-images .shape2 {
    right: 0;
    width: 200px;
  }
}
.hero5 .hero3-images .shape3 {
  position: absolute;
  bottom: 105px;
  left: 30px;
  z-index: -1;
}
@media (max-width: 767px) {
  .hero5 .hero3-images .shape3 {
    left: 30px;
    width: 200px;
    bottom: 180px;
  }
}
.hero5 .hero3-images .shape4 {
  position: absolute;
  left: 70px;
  top: 60px;
}
@media (max-width: 767px) {
  .hero5 .hero3-images .shape4 {
    left: 0;
    top: 18px;
  }
}
.hero5 .hero3-images .main-image-bg {
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
}

.inner-hero .sub-title img {
  transform: translateY(-3px);
  margin-right: 3px;
}
.inner-hero .hero1-single-slider {
  display: flex;
  align-items: center;
  height: 370px;
  margin-top: 86px;
  overflow: hidden;
}
.inner-hero .hero1-single-slider .page-prog {
  display: flex;
  align-items: center;
  padding-top: 12px;
}
.inner-hero .hero1-single-slider .page-prog .icon {
  padding: 0px 8px;
}
.inner-hero .hero1-single-slider .page-prog a, .inner-hero .hero1-single-slider .page-prog p {
  color: #FFF;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 100% */
  display: inline-block;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .inner-hero .hero1-single-slider {
    height: 500px;
  }
}
@media (max-width: 767px) {
  .inner-hero .hero1-single-slider {
    height: 500px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .inner-hero .hero1-single-slider .inner-heading {
    padding-top: 170px;
  }
}
@media (max-width: 767px) {
  .inner-hero .hero1-single-slider .inner-heading {
    padding-top: 170px;
  }
}
.inner-hero .hero1-single-slider .main-image {
  position: absolute;
  right: 0;
  top: -120px;
  z-index: 2;
  transition: all 2s ease-in-out;
  opacity: 0.9;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .inner-hero .hero1-single-slider .main-image {
    top: 250px;
  }
}
@media (max-width: 767px) {
  .inner-hero .hero1-single-slider .main-image {
    top: 270px;
  }
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .inner-hero .hero1-single-slider .main-image {
    transform: scale(0.8) translateX(450px);
  }
}
@media only screen and (min-width: 1600px) and (max-width: 1700px) {
  .inner-hero .hero1-single-slider .main-image {
    transform: scale(0.8) translateX(450px);
  }
}
.inner-hero .hero1-single-slider .main-image-shape {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 3;
  transition: all 2s ease-in-out;
  opacity: 1;
}
.inner-hero .hero1-single-slider .all-images {
  height: 340px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .inner-hero .hero1-single-slider .all-images {
    height: 500px;
  }
}
@media (max-width: 767px) {
  .inner-hero .hero1-single-slider .all-images {
    height: 500px;
  }
}
.inner-hero .hero1-single-slider .all-images .circle-area {
  position: absolute;
  z-index: 9;
  width: 200px;
  height: 200px;
  bottom: 40px;
  transition: all 1.5s;
}
@media (max-width: 767px) {
  .inner-hero .hero1-single-slider .all-images .circle-area {
    left: 0;
    top: 60px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .inner-hero .hero1-single-slider .all-images .circle-area {
    left: 0;
    top: 58px;
  }
}
.inner-hero .hero1-single-slider .all-images .circle-area .circle-arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -23px;
  margin-left: -22px;
  z-index: 9;
}

@media screen and (min-width: 1800px) {
  .inner-hero .hero1-single-slider .main-image {
    transform: scale(0.8) translateX(200px);
  }
}
/*
::::::::::::::::::::::::::
 HERO AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 PRELOADER AREA CSS
::::::::::::::::::::::::::
*/
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: var(--vtc-bg-white8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark-mode .preloader img {
  filter: brightness(0) invert(1);
}

.preloader.preloader2 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-container,
.loading {
  height: 140px;
  position: relative;
  width: 140px;
  border-radius: 100%;
}

.loading-container {
  margin: 40px auto;
}

.loading {
  border: 1px solid transparent;
  border-color: transparent var(--vtc-text-title-1) transparent var(--vtc-text-title-1);
  animation: rotate-loading 1.5s linear 0s infinite normal;
  transform-origin: 50% 50%;
}

.loading.loading2 {
  border: 1px solid transparent;
  border-color: transparent #4D32A5 transparent #4D32A5 !important;
  animation: rotate-loading 1.5s linear 0s infinite normal;
  transform-origin: 50% 50%;
}

.loading.loading5 {
  border: 1px solid transparent;
  border-color: transparent #9553FD transparent #9553FD;
  animation: rotate-loading 1.5s linear 0s infinite normal;
  transform-origin: 50% 50%;
}

.loading-container:hover .loading,
.loading-container .loading {
  transition: all 0.5s ease-in-out;
}

#loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90px;
  transform: translate(-50%, -50%);
}

@keyframes rotate-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/*
::::::::::::::::::::::::::
 PRELOADER AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  BACKGROUNDS AREA CSS
 ::::::::::::::::::::::::::
 */
.sec-bg1 {
  background-color: var(--vtc-bg-common-2);
}

.sec-bg2 {
  background-color: var(--vtc-bg-common-3);
}

.sec-bg3 {
  background-color: var(--vtc-bg-common-4);
}

.sec-bg4 {
  background-color: var(--vtc-bg-common-5);
}

.white-bg1 {
  background-color: var(--vtc-bg-white1);
}

.white-bg2 {
  background-color: var(--vtc-bg-white2);
}

.white-bg3 {
  background-color: var(--vtc-bg-white3);
}

.white-bg4 {
  background-color: var(--vtc-bg-white4);
}

.white-bg5 {
  background-color: var(--vtc-bg-white7);
}

.shape-bg1 {
  position: relative;
}
.shape-bg1::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-image: url(../img/bg/element-bg1.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

.shape-bg2 {
  position: relative;
}
.shape-bg2::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-image: url(../img/bg/service2-bg1.jpg);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: -2;
}

.shape-bg3 {
  position: relative;
}
.shape-bg3::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-image: url(../img/bg/case3-bg1.jpg);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: -2;
}

.shape-bg4 {
  background-color: var(--vtc-bg-common-14);
}

.shape-bg5 {
  background-color: var(--vtc-bg-common-17);
}

.dark-mode .shape-bg1 {
  position: relative;
}
.dark-mode .shape-bg1::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-image: url(../img/bg/element-bg1.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  filter: brightness(0) invert(1);
}
.dark-mode .shape-bg2 {
  position: relative;
}
.dark-mode .shape-bg2::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-image: url(../img/bg/service2-bg2.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: -2;
}
.dark-mode .shape-bg3 {
  position: relative;
}
.dark-mode .shape-bg3::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-image: url(../img/bg/case3-bg2.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: -2;
}

/*
::::::::::::::::::::::::::
 BACKGROUNDS AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  ANIMATIONS AREA CSS
 ::::::::::::::::::::::::::
 */
@keyframes round-circle {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(1000deg);
  }
}
@keyframes round-circle2 {
  0% {
    transform: rotate(-20deg);
  }
  100% {
    transform: rotate(20deg);
  }
}
@keyframes animate1 {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-20px);
  }
}
@keyframes animate2 {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(20px);
  }
}
@keyframes animate3 {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(15px);
  }
}
@keyframes animate4 {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-25px);
  }
}
@keyframes animate5 {
  0% {
    transform: scale(0.5);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes animate6 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}
.round-circle {
  position: relative;
  animation-name: round-circle;
  animation-duration: 72s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: linear;
}

.animate1 {
  position: relative;
  animation-name: animate1;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.animate2 {
  position: relative;
  animation-name: animate2;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.animate3 {
  position: relative;
  animation-name: animate3;
  animation-duration: 4s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.animate4 {
  position: relative;
  animation-name: animate4;
  animation-duration: 4s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.animate5 {
  position: relative;
  animation-name: animate5;
  animation-duration: 4s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.animate6 {
  position: relative;
  animation-name: animate6;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.round-circle2 {
  position: relative;
  animation-name: round-circle2;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;
}

.image-anime {
  overflow: hidden;
}

.image-anime:after {
  content: "";
  position: absolute;
  width: 200%;
  height: 0%;
  left: 50%;
  top: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%) rotate(-45deg);
  z-index: 1;
}

.image-anime:hover:after {
  height: 250%;
  transition: all 600ms linear;
  background-color: transparent;
}

/*
::::::::::::::::::::::::::
 ANIMATION AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  PAGINATION AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 PAGINATION AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  SEARCH AREA CSS
 ::::::::::::::::::::::::::
 */
.search__popup {
  padding: 70px;
  padding-top: 70px;
  padding-bottom: 100px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 360px;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 99999;
  transform: translateY(calc(-100% - 80px));
  transition: transform 0.6s ease-in-out, opacity 0.6s ease-in-out;
  transition-delay: 0.7s;
}

.search__popup.search-opened {
  transform: translateY(0%);
  transition-delay: 0s;
}

.search__popup.search-opened .search__input {
  transform: translateY(0px);
  opacity: 1;
  transition-delay: 0.3s;
}

.search__popup.search-opened .search__input::after {
  width: 100%;
  transition-delay: 0.5s;
}

.search__popup-2 {
  background-color: var(--tp-common-black-13);
}

.search__popup-2 .search__input .search-input-field ~ .search-focus-border {
  background-color: var(--tp-theme-8);
}

.search__popup-3 .search__input .search-input-field ~ .search-focus-border {
  background-color: var(--tp-theme-10);
}

.search__top {
  margin-bottom: 80px;
}

.search__input {
  position: relative;
  height: 80px;
  transition: all 0.3s ease-out 0s;
  transition-delay: 0.5s;
  opacity: 0;
}

.search__input::after {
  position: absolute;
  content: "";
  left: 0;
  bottom: 0;
  width: 0%;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease-out 0s;
  transition-delay: 0.3s;
}

.search__input input {
  width: 100%;
  height: 100%;
  background-color: transparent;
  border: 0;
  outline: 0;
  font-size: 24px;
  color: var(--tp-common-white);
  border-bottom: 1px solid transparent;
  padding: 0;
  padding-right: 30px;
  color: var(--vtc-text-white);
}

.search__input input::-moz-placeholder { /* Firefox 19+ */
  color: rgba(255, 255, 255, 0.5);
  font-size: 24px;
}

.search__input input::placeholder { /* MODERN BROWSER */
  color: rgba(255, 255, 255, 0.5);
  font-size: 24px;
}

.search__input button {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  font-size: 18px;
  color: var(--vtc-bg-white1);
  background: none;
  border: none;
}

.search__input .search-input-field ~ .search-focus-border {
  position: absolute;
  bottom: 0;
  left: auto;
  right: 0;
  width: 0;
  height: 1px;
  background-color: var(--tp-common-orange);
  transition: 0.5s 0.3s 0s ease-out;
}

.search__input .search-input-field:focus ~ .search-focus-border {
  width: 100%;
  left: 0;
  right: auto;
  transition: 0.5s 0.3s 0s ease-out;
}

.search__close-btn {
  font-size: 25px;
  color: rgba(255, 255, 255, 0.3);
  border: none;
  background: none;
}

.search__close-btn:hover {
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
}

/*
::::::::::::::::::::::::::
 SEARCH AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  COLOR AREA CSS
 ::::::::::::::::::::::::::
 */
.black1 {
  color: var(--vtc-text-title-1);
}

.black2 {
  color: var(--vtc-text-title-2);
}

.black3 {
  color: var(--vtc-text-title-3);
}

.white1 {
  color: var(--vtc-text-white);
}

.gray1 {
  color: var(--vtc-text-pera-1);
}

.gray2 {
  color: var(--vtc-text-pera-2);
}

.gray3 {
  color: var(--vtc-text-pera-4);
}

.gray4 {
  color: var(--vtc-text-pera-2);
}

.gray5 {
  color: var(--vtc-text-pera-5);
}

.gray6 {
  color: var(--vtc-text-pera-6);
}

.white-gray1 {
  color: var(--vtc-text-pera-3);
}

.mian-text1 {
  color: var(--vtc-bg-main2);
}

.sub-text1 {
  color: var(--vtc-text-sub-title-1);
}

.sub-text3 {
  color: var(--vtc-text-sub-title-2);
}

.text._hover1 {
  transition: all 0.4s;
}
.text._hover1:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main1);
}
.text._hover2 {
  transition: all 0.4s;
}
.text._hover2:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main3);
}
.text._hover3 {
  transition: all 0.4s;
}
.text._hover3:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main4);
}
.text._hover4 {
  transition: all 0.4s;
}
.text._hover4:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main6);
}
.text._hover5 {
  transition: all 0.4s;
}
.text._hover5:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main8);
}
.text._hover6 {
  transition: all 0.4s;
}
.text._hover6:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main12);
}

/*
::::::::::::::::::::::::::
 COLOR AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  THEME SETTINGS
 ::::::::::::::::::::::::::
 */
.theme-toggle-container {
  background-color: var(--vtc-bg-white1);
  height: 70px;
  position: fixed;
  top: 50%;
  z-index: 99;
  margin-top: -31px;
  right: 0;
  width: 40px;
  border-radius: 20px 0px 0px 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.13);
}

/* Style for the dark/light mode toggle switch */
.theme-switch {
  display: inline-block;
  width: 60px;
  height: 30px;
  transform: rotate(90deg) translateX(20px) translateY(0px);
  margin-left: -8px;
  /* Show the sun icon in light mode and hide the moon icon */
  /* Icons for dark and light mode */
}
.theme-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.theme-switch input:checked + .slider {
  background: #F0C73C; /* Change background color for light mode */
}
.theme-switch input:checked + .slider:before {
  transform: translateX(30px); /* Move the circle to the right */
}
.theme-switch input:checked + .slider .sun-icon {
  opacity: 0;
}
.theme-switch input:checked + .slider .moon-icon {
  opacity: 1;
}
.theme-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #dfdfdf;
  transition: 0.4s;
  border-radius: 34px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5px;
}
.theme-switch .slider::before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  background-color: white;
  border-radius: 50%;
  transition: 0.4s;
  left: 2px;
  top: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  opacity: 0.5;
}
.theme-switch .icon {
  font-size: 16px;
  color: #fff;
  position: absolute;
  opacity: 0;
  transition: opacity 0.3s;
}
.theme-switch .sun-icon {
  left: 8px;
  opacity: 1; /* Show moon icon by default for dark mode */
  color: rgba(0, 0, 0, 0.5411764706);
}
.theme-switch .moon-icon {
  right: 8px;
  opacity: 0; /* Hide sun icon by default */
}

.dark-mode {
  --vtc-text-title-1: #fff;
  --vtc-text-title-3: #fff;
  --vtc-text-title-5: #fff;
  --vtc-text-title-6: #fff;
  --vtc-text-title-7: #fff;
  --vtc-text-pera-4: #ffffffc7;
  --vtc-text-pera-7: #ffffffd7;
  --vtc-text-sub-title-2: #fff;
  --vtc-text-sub-title-3: #fff;
  --vtc-text-sub-title-4: #fff;
  --vtc-bg-common-2: #1D393E;
  --vtc-bg-common-5: #ffffff09;
  --vtc-bg-common-6: #252635;
  --vtc-bg-common-7: #252635;
  --vtc-bg-common-9: rgba(255, 255, 255, 0.10);
  --vtc-bg-common-10: rgba(255, 255, 255, 0.10);
  --vtc-bg-common-12: rgba(255, 255, 255, 0.10);
  --vtc-bg-common-13: rgba(255, 255, 255, 0.1);
  --vtc-bg-common-15: rgba(255, 255, 255, 0.1);
  --vtc-bg-common-14: #ffffff0c;
  --vtc-bg-common-17: #25184A;
  --vtc-bg-white1: #ffff;
  --vtc-bg-white2: #1D393E;
  --vtc-bg-white5: #ffffff15;
  --vtc-bg-white6: #333342;
  --vtc-bg-white7: #061D19;
  --vtc-bg-white8: #17093E;
  --vtc-bg-white9: #ffffff11;
  --vtc-bg-main3: #fff;
  --vtc-bg-main9: #fff;
  --vtc-bg-white3: #0F2C32;
  --vtc-bg-white4: #0D0E1F;
  --vtc-bg-common-3: #274146;
  --vtc-text-sub-title-1: #fff;
  --vtc-text-pera-2: #ffffffc0;
  --vtc-text-pera-1: #ffffffc0;
  --vtc-border-2: #ffffff2a;
  --vtc-border-4: #ffffff33;
}

.blok:nth-of-type(odd) {
  background-color: white;
}

.blok:nth-of-type(even) {
  background-color: black;
}
/* #Progress
================================================== */
.progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
  z-index: 1;
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  transition: all 200ms linear;
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  z-index: 999;
}

.progress-wrap::after {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  color: var(--vtc-text-title-1);
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 1;
  transition: all 200ms linear;
}

.progress-wrap:hover::after {
  opacity: 0;
}

.progress-wrap::before {
  position: absolute;
  font-family: "FontAwesome";
  content: "\f062";
  text-align: center;
  line-height: 56px;
  font-size: 18px;
  opacity: 0;
  left: 0;
  top: 0;
  height: 56px;
  width: 56px;
  cursor: pointer;
  display: block;
  z-index: 2;
  transition: all 200ms linear;
}

.progress-wrap:hover::before {
  opacity: 1;
}

.progress-wrap svg path {
  fill: none;
}

.progress-wrap svg.progress-circle path {
  stroke: var(--vtc-text-title-1); /* --- Lijn progres kleur --- */
  stroke-width: 4;
  box-sizing: border-box;
  transition: all 200ms linear;
}

/*
 ::::::::::::::::::::::::::
   THEME SETTINGS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 BLOG AREA CSS
::::::::::::::::::::::::::
*/
.details-content .image img {
  width: 100%;
  border-radius: 8px;
}
.details-content .detials-check-list {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-16); /* 100% */
  display: inline-flex;
  align-items: center;
  margin-top: 20px;
}
.details-content .detials-check-list span.check {
  background-color: var(--vtc-bg-common-1);
  height: 20px;
  width: 20px;
  text-align: center;
  line-height: 20px;
  border-radius: 50%;
  font-size: 12px;
  display: inline-block;
  color: var(--vtc-bg-white1);
  margin-right: 6px;
}
.details-content .detials-check-list2 {
  background-color: var(--vtc-bg-common-2);
  border-radius: 8px;
  padding: 24px;
  width: 100%;
  transition: all 0.4s;
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-16); /* 100% */
  display: inline-flex;
  align-items: center;
  margin-top: 20px;
}
.details-content .detials-check-list2 span.check {
  background-color: var(--vtc-bg-common-1);
  height: 28px;
  width: 28px;
  text-align: center;
  line-height: 28px;
  border-radius: 50%;
  font-size: 15px;
  display: inline-block;
  color: var(--vtc-bg-white1);
  margin-right: 6px;
  transition: all 0.4s;
}
.details-content .detials-check-list2:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-common-1);
  transform: translateY(-5px);
  color: var(--vtc-bg-white1);
}
.details-content .detials-check-list2:hover .check {
  background-color: var(--vtc-bg-white1);
  color: var(--vtc-bg-main2);
  transition: all 0.4s;
}
.details-content .about-page-progress-area {
  background-color: var(--vtc-bg-common-2);
  padding: 20px;
  border-radius: 8px;
  margin-top: 30px;
}
.details-content .about-page-progress-area .text-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.details-content .about-page-progress-area .text-area p {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
}
.details-content .about-page-progress-area .progress {
  --bs-progress-height: 1rem;
  --bs-progress-font-size: 0.75rem;
  --bs-progress-bg: #DBDEDE;
  --bs-progress-border-radius: 30px;
  --bs-progress-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --bs-progress-bar-color: #fff;
  --bs-progress-bar-bg: #10343B;
  --bs-progress-bar-transition: width 0.6s ease;
  display: flex;
  height: var(--bs-progress-height);
  overflow: hidden;
  font-size: var(--bs-progress-font-size);
  background-color: var(--bs-progress-bg);
  border-radius: var(--bs-progress-border-radius);
}
.details-content .tags-social-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid var(--vtc-border-1);
  margin-top: 24px;
  padding: 24px 0px;
}
@media (max-width: 767px) {
  .details-content .tags-social-area {
    display: block;
  }
}
.details-content .tags-social-area .tags ul li {
  display: inline-block;
  margin-right: 16px;
}
.details-content .tags-social-area .tags ul .text {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
}
.details-content .tags-social-area .tags ul .tag a {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
  text-transform: capitalize;
  display: inline-block;
  background-color: var(--vtc-bg-common-2);
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.4s;
}
.details-content .tags-social-area .tags ul .tag a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main1);
}
@media (max-width: 767px) {
  .details-content .tags-social-area .social-area {
    padding-top: 20px;
  }
}
.details-content .tags-social-area .social-area ul .text {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
}
.details-content .tags-social-area .social-area ul li {
  display: inline-block;
  margin-right: 8px;
}
.details-content .tags-social-area .social-area ul .icon a {
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  background-color: var(--vtc-bg-common-2);
  border-radius: 50%;
  color: var(--vtc-text-title-1);
  transition: all 0.4s;
}
.details-content .tags-social-area .social-area ul .icon a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main1);
}

.comment-area .single-comment {
  background-color: var(--vtc-bg-common-2);
  border-radius: 8px;
  padding: 32px;
}
.comment-area .single-comment .top-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.comment-area .single-comment .top-area .author-area {
  display: flex;
  align-items: center;
}
.comment-area .single-comment .top-area .author-area .text {
  padding-left: 16px;
}
.comment-area .single-comment .top-area .author-area .text .date {
  display: inline-block;
  color: var(--vtc-text-pera-1);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 16px; /* 100% */
}
.comment-area .single-comment .top-area .author-area .text .date img {
  transform: translateY(-3px);
  margin-right: 3px;
  opacity: 0.7;
}
.comment-area .single-comment .top-area .author-area .text h4 a {
  display: inline-block;
  color: var(--vtc-text-title-1);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 100% */
  transition: all 0.4s;
  padding-top: 8px;
}
.comment-area .single-comment .top-area .author-area .text h4 a:hover {
  transition: all 0.4;
  color: var(--vtc-bg-main1);
}
.comment-area .single-comment .top-area .reply a {
  display: inline-block;
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-16); /* 100% */
  text-transform: capitalize;
  transition: all 0.4s;
}
.comment-area .single-comment .top-area .reply a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main1);
}
.comment-area .single-comment p {
  color: var(--vtc-text-pera-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-26); /* 144.444% */
  padding-top: 30px;
}

.details-contact .form-area {
  padding: 32px;
  border-radius: 8px;
  background-color: var(--vtc-bg-common-2);
  margin-top: 40px;
}
.details-contact .form-area h4 {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 100% */
}
.details-contact .form-area .single-input {
  margin-top: 20px;
}
.details-contact .form-area .single-input input, .details-contact .form-area .single-input textarea {
  background-color: var(--vtc-bg-white3);
  padding: 16px;
  border: none;
  width: 100%;
  border-radius: 8px;
}
.details-contact .form-area .single-input input:focus, .details-contact .form-area .single-input textarea:focus {
  outline: none;
}
.details-contact .form-area .single-input input::-moz-placeholder, .details-contact .form-area .single-input textarea::-moz-placeholder {
  color: var(--vtc-text-pera-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16);
}
.details-contact .form-area .single-input input::placeholder, .details-contact .form-area .single-input textarea::placeholder {
  color: var(--vtc-text-pera-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16);
}

.dark-mode .comment-area .top-area .author-area .date {
  display: inline-block;
  color: var(--vtc-text-pera-1);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 16px; /* 100% */
}
.dark-mode .comment-area .top-area .author-area .date img {
  transform: translateY(-3px);
  margin-right: 3px;
  opacity: 0.7;
  filter: brightness(40);
}

/*
::::::::::::::::::::::::::
 BLOG AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BLOG SIDEBAR AREA CSS
::::::::::::::::::::::::::
*/
.sidebar-area ._sidebar-contact_widget {
  background-color: var(--vtc-bg-common-1);
  padding: 28px 24px;
  border-radius: 8px;
}
.sidebar-area ._sidebar-contact_widget h3 {
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 100% */
  margin-bottom: 24px;
}
.sidebar-area ._sidebar-contact_widget .buttons {
  text-align: end;
}
.sidebar-area ._sidebar-contact_widget ._project-list ul li {
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  border-bottom: 1px solid rgba(255, 255, 255, 0.0941176471);
  padding: 20px 0px;
}
.sidebar-area ._sidebar-contact_widget ._project-list ul li span {
  font-weight: var(--f-fw-medium) !important;
  opacity: 0.7;
  display: inline-block;
  padding-right: 10px;
}
.sidebar-area ._sidebar-contact_widget ._social {
  padding-top: 20px;
  display: flex;
  align-items: center;
}
.sidebar-area ._sidebar-contact_widget ._social .text p {
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-bold);
  line-height: var(--f-fs-font-18); /* 100% */
  opacity: 0.7;
}
.sidebar-area ._sidebar-contact_widget ._social .icons {
  margin-left: 20px;
}
.sidebar-area ._sidebar-contact_widget ._social .icons ul li {
  display: inline-block;
}
.sidebar-area ._sidebar-contact_widget ._social .icons ul li a {
  display: inline-block;
  height: 28px;
  width: 28px;
  text-align: center;
  line-height: 28px;
  border-radius: 50%;
  color: var(--vtc-bg-main1);
  background-color: rgba(255, 255, 255, 0.1098039216);
  font-size: 14px;
  margin-left: 2px;
  transition: all 0.4s;
}
.sidebar-area ._sidebar-contact_widget ._social .icons ul li a:hover {
  transition: all 0.4s;
  color: var(--vtc-text-title-1);
  background-color: var(--vtc-bg-main1);
}
.sidebar-area ._sidebar-contact_widget ._contact-form input, .sidebar-area ._sidebar-contact_widget ._contact-form textarea {
  background-color: rgba(255, 255, 255, 0.0941176471);
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  color: var(--vtc-bg-white1);
  margin-bottom: 16px;
}
.sidebar-area ._sidebar-contact_widget ._contact-form input::-moz-placeholder, .sidebar-area ._sidebar-contact_widget ._contact-form textarea::-moz-placeholder {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
}
.sidebar-area ._sidebar-contact_widget ._contact-form input::placeholder, .sidebar-area ._sidebar-contact_widget ._contact-form textarea::placeholder {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
}
.sidebar-area ._sidebar-contact_widget ._contact-form input:focus, .sidebar-area ._sidebar-contact_widget ._contact-form textarea:focus {
  outline: none;
}
.sidebar-area ._sidebar-widget {
  background-color: var(--vtc-bg-common-2);
  padding: 28px 24px;
  border-radius: 8px;
}
.sidebar-area ._sidebar-widget h3 {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 100% */
  margin-bottom: 24px;
}
.sidebar-area ._sidebar-widget._list ul li a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-18);
  line-height: var(--f-fs-font-18);
  font-weight: var(--f-fw-bold);
  transition: all 0.4s;
  background-color: var(--vtc-bg-white3);
  padding: 20px 24px;
  margin-bottom: 20px;
  border-radius: 8px;
}
.sidebar-area ._sidebar-widget._list ul li a:hover {
  background-color: var(--vtc-bg-main1);
  transition: all 0.4s;
  color: var(--vtc-text-title-1);
}
.sidebar-area ._sidebar-widget._tags .tags-list ul li {
  display: inline-block;
}
.sidebar-area ._sidebar-widget._tags .tags-list ul li a {
  display: inline-block;
  background-color: var(--vtc-bg-white3);
  padding: 10px 14px;
  border-radius: 8px;
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
  text-transform: capitalize;
  transition: all 0.4s;
  margin-right: 10px;
  margin-top: 14px;
}
.sidebar-area ._sidebar-widget._tags .tags-list ul li a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main1);
}
.sidebar-area ._sidebar-widget._search form input {
  padding: 16px;
  width: 100%;
  border: none;
  background-color: var(--vtc-bg-white3);
  border-radius: 8px;
  color: var(--vtc-text-title-1);
}
.sidebar-area ._sidebar-widget._search form input:focus {
  outline: none;
}
.sidebar-area ._sidebar-widget._search form input::-moz-placeholder {
  color: var(--vtc-text-title-1);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 100% */
  opacity: 0.7;
}
.sidebar-area ._sidebar-widget._search form input::placeholder {
  color: var(--vtc-text-title-1);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 100% */
  opacity: 0.7;
}
.sidebar-area ._sidebar-widget._search form button {
  border: none;
  background: none;
  color: var(--vtc-text-title-1);
  position: absolute;
  top: 0;
  right: 0;
  font-size: var(--f-fs-font-18);
  top: 15px;
  right: 12px;
}
.sidebar-area ._sidebar-widget._author .author-images ul li {
  display: inline-block;
  margin: 9px 5px;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post {
  display: flex;
  align-items: center;
  margin-top: 20px;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .image {
  width: 120px;
  border-radius: 8px;
  overflow: hidden;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .image img {
  width: 120px;
  transition: all 0.4s;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content {
  padding-left: 20px;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content a.date {
  display: inline-block;
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-16); /* 100% */
  text-transform: uppercase;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content a.date img {
  transform: translateY(-3px);
  margin-right: 3px;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content h4 a {
  display: inline-block;
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 133.333% */
  text-transform: capitalize;
  padding-top: 12px;
  transition: all 0.4s;
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content h4 a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main1);
}
.sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post:hover .image img {
  transition: all 0.4s;
  filter: grayscale(1);
  transform: scale(1.1) rotate(2deg);
}

.dark-mode .sidebar-area ._sidebar-widget._recent .recent-blog .recent-blog-post .content .date img {
  filter: brightness(40);
}

/*
::::::::::::::::::::::::::
 BLOG SIDEBAR AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BLOG POST BOX AREA CSS
::::::::::::::::::::::::::
*/
.vl-blog-1-item {
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
  transition: all 0.4s;
  border-radius: 8px;
}
.vl-blog-1-item .vl-blog-3-thumb {
  border-radius: 8px 8px 0px 0px;
  max-height: 300px;
}
.vl-blog-1-item .vl-blog-3-thumb img {
  transition: all 0.4s;
}
.vl-blog-1-item .vl-blog1-meta .user {
  position: absolute;
  bottom: 0;
  left: 12px;
}
.vl-blog-1-item .vl-blog1-meta .user img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.vl-blog-1-item .vl-blog1-meta .date {
  display: inline-block;
}
.vl-blog-1-item .vl-blog1-meta .date img {
  transform: translateY(-3px);
  margin-right: 3px;
}
.vl-blog-1-item:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.vl-blog-1-item:hover .vl-blog-3-thumb img {
  transition: all 0.4s;
  transform: rotate(2deg) scale(1.1);
  filter: grayscale(1);
}
.vl-blog-1-item:hover .vl-blog-1-content .learn1 {
  display: inline-block;
  overflow: hidden;
}
.vl-blog-1-item:hover .vl-blog-1-content .learn1 .arrow2 {
  transform: translateY(-12px) rotate(-45deg) translateX(-18px);
  transition: all 0.4s;
  opacity: 1;
}
.vl-blog-1-item:hover .vl-blog-1-content .learn1 .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.dark-mode .vl-blog-1-item .vl-blog1-meta .date img {
  filter: brightness(40);
}

.vl-blog-2-item {
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
  transition: all 0.4s;
  border-radius: 8px;
  position: relative;
}
.vl-blog-2-item .vl-blog-3-thumb {
  border-radius: 8px 8px 0px 0px;
}
.vl-blog-2-item .vl-blog-3-thumb img {
  transition: all 0.4s;
}
.vl-blog-2-item .vl-blog-2-content {
  position: absolute;
  bottom: 30px;
  background-color: var(--vtc-bg-white1);
  margin: 0px 30px;
  border-radius: 16px;
}
.vl-blog-2-item .vl-blog-2-content p {
  color: #37385C;
}
.vl-blog-2-item .vl-blog-2-content .learn3 {
  position: absolute;
  top: -15px;
  right: -15px;
}
.vl-blog-2-item .vl-blog1-meta .user img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.vl-blog-2-item .vl-blog1-meta .date {
  display: inline-block;
  margin-right: 30px;
  position: relative;
}
.vl-blog-2-item .vl-blog1-meta .date img {
  transform: translateY(-3px);
  margin-right: 3px;
}
.vl-blog-2-item .vl-blog1-meta .date::after {
  content: "";
  position: absolute;
  right: -16px;
  top: 0;
  height: 16px;
  width: 1px;
  background-color: var(--vtc-text-pera-6);
  opacity: 0.5;
}
.vl-blog-2-item:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.vl-blog-2-item:hover .vl-blog-3-thumb img {
  transition: all 0.4s;
  transform: rotate(2deg) scale(1.1);
  filter: grayscale(1);
}
.vl-blog-2-item:hover .vl-blog-2-content .learn3 span {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main4);
}
.vl-blog-2-item:hover .vl-blog-2-content .learn3 .arrow-all {
  transform: translateX(3px);
  transition: all 0.4s;
}
.vl-blog-2-item:hover .vl-blog-2-content .learn3 .arrow-all .arrow2 {
  transform: translateY(-7px) rotate(-45deg) translateX(-10px);
  transition: all 0.4s;
  opacity: 1;
}
.vl-blog-2-item:hover .vl-blog-2-content .learn3 .arrow-all .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.vl-blog-3-item {
  background-color: var(--vtc-bg-white7);
  border-radius: 16px;
  transition: all 0.4s;
}
.vl-blog-3-item .image-area {
  border-radius: 16px 16px 0px 0px;
  overflow: hidden;
  position: relative;
}
.vl-blog-3-item .image-area .vl-blog-3-thumb img {
  width: 100%;
  transition: all 0.4s;
}
.vl-blog-3-item .image-area .arrow {
  display: inline-block;
  height: 56px;
  width: 56px;
  text-align: center;
  border-radius: 50%;
  color: var(--vtc-bg-white1);
  position: absolute;
  right: 20px;
  bottom: 20px;
  transition: all 0.4s;
}
.vl-blog-3-item .image-area .arrow:hover {
  transition: all 0.4s;
}
.vl-blog-3-item .content-area {
  padding: 32px;
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.09);
  border-radius: 0px 0 16px 16px;
}
.vl-blog-3-item .content-area .vl-blog3-meta a {
  display: inline-block;
  color: var(--vtc-text-title-5);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
  margin-right: 30px;
  position: relative;
}
.vl-blog-3-item .content-area .vl-blog3-meta a img {
  transform: translateY(-3px);
  margin-right: 2px;
  transition: all 0.4s;
}
.vl-blog-3-item .content-area .vl-blog3-meta a.add-after::after {
  content: "";
  position: absolute;
  top: 2px;
  right: -18px;
  height: 16px;
  width: 1px;
  background-color: var(--vtc-border-1);
}
.vl-blog-3-item .content-area h4 a {
  display: inline-block;
  color: var(--vtc-text-title-5);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-32); /* 133.333% */
  transition: all 0.4s;
  padding-top: 20px;
}
.vl-blog-3-item .content-area h4 a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main6);
}
.vl-blog-3-item:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.vl-blog-3-item:hover .image-area .vl-blog-3-thumb img {
  transition: all 0.4s;
  transform: scale(1.1) rotate(2deg);
  filter: grayscale(1);
}
.vl-blog-3-item:hover .learn2 span {
  transition: all 0.4s;
  background-color: #a7d35a;
}
.vl-blog-3-item:hover .learn2 .arrow-all {
  transform: translateX(3px);
  transition: all 0.4s;
}
.vl-blog-3-item:hover .learn2 .arrow-all .arrow2 {
  transform: translateY(-7px) rotate(-45deg) translateX(-10px);
  transition: all 0.4s;
  opacity: 1;
}
.vl-blog-3-item:hover .learn2 .arrow-all .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.dark-mode .vl-blog-3-item .vl-blog3-meta a img {
  filter: brightness(0) invert(1);
}

.vl-blog-4-item {
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
  transition: all 0.4s;
  border-radius: 8px;
  position: relative;
}
@media (max-width: 767px) {
  .vl-blog-4-item {
    margin-top: 150px;
  }
}
.vl-blog-4-item .vl-blog-3-thumb {
  border-radius: 8px 8px 0px 0px;
}
.vl-blog-4-item .vl-blog-3-thumb img {
  transition: all 0.4s;
}
.vl-blog-4-item .learn3 span {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main8);
}
.vl-blog-4-item .vl-blog-2-content {
  position: absolute;
  bottom: 30px;
  background-color: var(--vtc-bg-white1);
  margin: 0px 30px;
  border-radius: 16px;
}
.vl-blog-4-item .vl-blog-2-content p {
  color: #37385C;
}
.vl-blog-4-item .vl-blog-2-content .learn3 {
  position: absolute;
  top: -15px;
  right: -15px;
}
.vl-blog-4-item .vl-blog1-meta .user img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.vl-blog-4-item .vl-blog1-meta .date {
  display: inline-block;
  margin-right: 30px;
  position: relative;
}
.vl-blog-4-item .vl-blog1-meta .date img {
  transform: translateY(-3px);
  margin-right: 3px;
}
.vl-blog-4-item .vl-blog1-meta .date::after {
  content: "";
  position: absolute;
  right: -16px;
  top: 0;
  height: 16px;
  width: 1px;
  background-color: var(--vtc-text-pera-6);
  opacity: 0.5;
}
.vl-blog-4-item:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.vl-blog-4-item:hover .vl-blog-3-thumb img {
  transition: all 0.4s;
  transform: rotate(2deg) scale(1.1);
}
.vl-blog-4-item:hover .vl-blog-2-content .learn3 span {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main8);
}
.vl-blog-4-item:hover .vl-blog-2-content .learn3 .arrow-all {
  transform: translateX(3px);
  transition: all 0.4s;
}
.vl-blog-4-item:hover .vl-blog-2-content .learn3 .arrow-all .arrow2 {
  transform: translateY(-7px) rotate(-45deg) translateX(-10px);
  transition: all 0.4s;
  opacity: 1;
}
.vl-blog-4-item:hover .vl-blog-2-content .learn3 .arrow-all .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.vl-blog-5-item {
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
  transition: all 0.4s;
  border-radius: 8px;
  background-color: var(--vtc-bg-white9);
}
.vl-blog-5-item .vl-blog-3-thumb {
  border-radius: 8px 8px 0px 0px;
  max-height: 300px;
}
.vl-blog-5-item .vl-blog-3-thumb img {
  transition: all 0.4s;
}
.vl-blog-5-item .vl-blog1-meta .user {
  position: absolute;
  bottom: 0;
  left: 12px;
}
.vl-blog-5-item .vl-blog1-meta .user img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.vl-blog-5-item .vl-blog1-meta .date {
  display: inline-block;
}
.vl-blog-5-item .vl-blog1-meta .date img {
  transform: translateY(-3px);
  margin-right: 3px;
}
.vl-blog-5-item:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.vl-blog-5-item:hover .vl-blog-3-thumb img {
  transition: all 0.4s;
  transform: rotate(2deg) scale(1.1);
  filter: grayscale(1);
}
.vl-blog-5-item:hover .vl-blog-1-content .learn1 {
  display: inline-block;
  overflow: hidden;
}
.vl-blog-5-item:hover .vl-blog-1-content .learn1 .arrow2 {
  transform: translateY(-12px) rotate(-45deg) translateX(-18px);
  transition: all 0.4s;
  opacity: 1;
}
.vl-blog-5-item:hover .vl-blog-1-content .learn1 .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.dark-mode .vl-blog-5-item .vl-blog1-meta .date img {
  filter: brightness(40);
}

.vl-blog-page1-item {
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
  transition: all 0.4s;
  border-radius: 8px;
  position: relative;
}
@media (max-width: 767px) {
  .vl-blog-page1-item {
    margin-top: 150px;
  }
}
.vl-blog-page1-item .vl-blog-3-thumb {
  border-radius: 8px 8px 0px 0px;
}
.vl-blog-page1-item .vl-blog-3-thumb img {
  transition: all 0.4s;
}
.vl-blog-page1-item .learn3 span {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main1);
  color: var(--vtc-text-title-1);
}
.vl-blog-page1-item .vl-blog-2-content {
  position: absolute;
  bottom: 30px;
  background-color: var(--vtc-bg-white1);
  margin: 0px 30px;
  border-radius: 16px;
}
.vl-blog-page1-item .vl-blog-2-content p {
  color: #37385C;
}
.vl-blog-page1-item .vl-blog-2-content .learn3 {
  position: absolute;
  top: -15px;
  right: -15px;
}
.vl-blog-page1-item .vl-blog1-meta .user img {
  transform: translateY(-2px);
  margin-right: 3px;
}
.vl-blog-page1-item .vl-blog1-meta .date {
  display: inline-block;
  margin-right: 30px;
  position: relative;
}
.vl-blog-page1-item .vl-blog1-meta .date img {
  transform: translateY(-3px);
  margin-right: 3px;
}
.vl-blog-page1-item .vl-blog1-meta .date::after {
  content: "";
  position: absolute;
  right: -16px;
  top: 0;
  height: 16px;
  width: 1px;
  background-color: var(--vtc-text-pera-6);
  opacity: 0.5;
}
.vl-blog-page1-item:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.vl-blog-page1-item:hover .vl-blog-3-thumb img {
  transition: all 0.4s;
  transform: rotate(2deg) scale(1.1);
  filter: grayscale(1);
}
.vl-blog-page1-item:hover .vl-blog-2-content .learn3 span {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main1);
}
.vl-blog-page1-item:hover .vl-blog-2-content .learn3 .arrow-all {
  transform: translateX(3px);
  transition: all 0.4s;
}
.vl-blog-page1-item:hover .vl-blog-2-content .learn3 .arrow-all .arrow2 {
  transform: translateY(-7px) rotate(-45deg) translateX(-10px);
  transition: all 0.4s;
  opacity: 1;
}
.vl-blog-page1-item:hover .vl-blog-2-content .learn3 .arrow-all .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

/*
::::::::::::::::::::::::::
 BLOG POST BOX AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BLOG RECENT POST AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 BLOG RECENT POST AREA CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 1 CSS
::::::::::::::::::::::::::
*/
.vl-copyright1._dv-top {
  border-top: 1px solid var(--vtc-border-4);
}

.vl-footer-thumb {
  width: 70px;
  margin-bottom: 16px;
}
.vl-footer-thumb img {
  width: 100%;
  border-radius: 8px;
  transition: all 0.4s;
}
.vl-footer-thumb a {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  height: 26px;
  width: 26px;
  text-align: center;
  line-height: 26px;
  color: var(--vtc-text-title-1);
  background-color: var(--vtc-bg-main1);
  margin-top: -13px;
  margin-left: -13px;
  border-radius: 4px;
  transition: all 0.4s;
  transform: scale(1.2);
  opacity: 0;
}
.vl-footer-thumb:hover {
  transition: all 0.4s;
}
.vl-footer-thumb:hover img {
  transition: all 0.4s;
  filter: grayscale(1);
}
.vl-footer-thumb:hover a {
  transform: scale(1);
  transition: all 0.4s;
  opacity: 1;
}

.vl-footer-thumb-box {
  -moz-column-gap: 16px;
       column-gap: 16px;
}

.vl-footer-area1 {
  padding-top: 213px;
}

.vl-copyright-social a {
  display: inline-block;
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 50%;
  background-color: var(--vtc-bg-white3);
  color: var(--vtc-text-title-1);
  font-size: 16px;
  margin-right: 3px;
  transition: all 0.4s;
}
.vl-copyright-social a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main1);
}

.vl-footer-list a {
  transition: all 0.4s;
}
.vl-footer-list a:hover {
  transform: translateX(5px);
  transition: all 0.4s;
}

.vl-footer-area1 .black-logo {
  display: block;
}
.vl-footer-area1 .white-logo {
  display: none;
}

.dark-mode .vl-footer-area1 .black-logo {
  display: none;
}
.dark-mode .vl-footer-area1 .white-logo {
  display: block;
}
.dark-mode .single-contact .icon img {
  filter: brightness(40);
}

/*
::::::::::::::::::::::::::
 FOOTER AREA 1 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 2 CSS
::::::::::::::::::::::::::
*/
.vl-copyright1._dv-top {
  border-top: 1px solid var(--vtc-border-4);
}

.vl-footer-thumb2 {
  width: 70px;
  margin-bottom: 16px;
}
.vl-footer-thumb2 img {
  width: 100%;
  border-radius: 8px;
  transition: all 0.4s;
}
.vl-footer-thumb2 a {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  height: 26px;
  width: 26px;
  text-align: center;
  line-height: 26px;
  color: var(--vtc-bg-white1);
  background-color: var(--vtc-bg-main4);
  margin-top: -13px;
  margin-left: -13px;
  border-radius: 4px;
  transition: all 0.4s;
  transform: scale(1.2);
  opacity: 0;
}
.vl-footer-thumb2:hover {
  transition: all 0.4s;
}
.vl-footer-thumb2:hover img {
  transition: all 0.4s;
  filter: grayscale(1);
}
.vl-footer-thumb2:hover a {
  transform: scale(1);
  transition: all 0.4s;
  opacity: 1;
}

.vl-footer-thumb-box {
  -moz-column-gap: 16px;
       column-gap: 16px;
}

.vl-footer-area3 {
  padding-top: 100px;
}

.vl-copyright-social2 a {
  display: inline-block;
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 50%;
  background-color: var(--vtc-bg-common-9);
  color: var(--vtc-text-title-1);
  font-size: 16px;
  margin-right: 3px;
  transition: all 0.4s;
}
.vl-copyright-social2 a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main4);
  color: var(--vtc-bg-white1);
}

.vl-footer-list a {
  transition: all 0.4s;
}
.vl-footer-list a:hover {
  transform: translateX(5px);
  transition: all 0.4s;
}

.vl-footer-area1 .black-logo {
  display: block;
}
.vl-footer-area1 .white-logo {
  display: none;
}

.dark-mode .vl-footer-area1 .black-logo {
  display: none;
}
.dark-mode .vl-footer-area1 .white-logo {
  display: block;
}
.dark-mode .single-contact .icon img {
  filter: brightness(40);
}

/*
::::::::::::::::::::::::::
 FOOTER AREA 2 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 3 CSS
::::::::::::::::::::::::::
*/
.vl-copyright1._dv-top {
  border-top: 1px solid var(--vtc-border-4);
}

.vl-footer-thumb3 {
  width: 70px;
  margin-bottom: 16px;
}
.vl-footer-thumb3 img {
  width: 100%;
  border-radius: 8px;
  transition: all 0.4s;
}
.vl-footer-thumb3 a {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  height: 26px;
  width: 26px;
  text-align: center;
  line-height: 26px;
  color: var(--vtc-bg-white1);
  background-color: var(--vtc-bg-main6);
  margin-top: -13px;
  margin-left: -13px;
  border-radius: 4px;
  transition: all 0.4s;
  transform: scale(1.2);
  opacity: 0;
}
.vl-footer-thumb3:hover {
  transition: all 0.4s;
}
.vl-footer-thumb3:hover img {
  transition: all 0.4s;
  filter: grayscale(1);
}
.vl-footer-thumb3:hover a {
  transform: scale(1);
  transition: all 0.4s;
  opacity: 1;
}

.vl-footer-thumb-box {
  -moz-column-gap: 16px;
       column-gap: 16px;
}

.vl-footer-area1 {
  padding-top: 100px;
}

.vl-copyright-social3 a {
  display: inline-block;
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 50%;
  background-color: var(--vtc-bg-common-9);
  color: var(--vtc-text-title-1);
  font-size: 16px;
  margin-right: 3px;
  transition: all 0.4s;
}
.vl-copyright-social3 a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main6);
  color: var(--vtc-bg-white1);
}

.vl-footer-list a {
  transition: all 0.4s;
}
.vl-footer-list a:hover {
  transform: translateX(5px);
  transition: all 0.4s;
}

.vl-footer-area3 .black-logo {
  display: block;
}
.vl-footer-area3 .white-logo {
  display: none;
}

.dark-mode .vl-footer-area3 .black-logo {
  display: none;
}
.dark-mode .vl-footer-area3 .white-logo {
  display: block;
}
.dark-mode .single-contact .icon img {
  filter: brightness(40);
}

/*
::::::::::::::::::::::::::
 FOOTER AREA 3 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 4 CSS
::::::::::::::::::::::::::
*/
.vl-copyright1._dv-top {
  border-top: 1px solid var(--vtc-border-4);
}

.vl-footer-thumb4 {
  width: 70px;
  margin-bottom: 16px;
}
.vl-footer-thumb4 img {
  width: 100%;
  border-radius: 8px;
  transition: all 0.4s;
}
.vl-footer-thumb4 a {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  height: 26px;
  width: 26px;
  text-align: center;
  line-height: 26px;
  color: var(--vtc-bg-white1);
  background-color: var(--vtc-bg-main8);
  margin-top: -13px;
  margin-left: -13px;
  border-radius: 4px;
  transition: all 0.4s;
  transform: scale(1.2);
  opacity: 0;
}
.vl-footer-thumb4:hover {
  transition: all 0.4s;
}
.vl-footer-thumb4:hover img {
  transition: all 0.4s;
  filter: grayscale(1);
}
.vl-footer-thumb4:hover a {
  transform: scale(1);
  transition: all 0.4s;
  opacity: 1;
}

.vl-footer-thumb-box {
  -moz-column-gap: 16px;
       column-gap: 16px;
}

.vl-footer-area4 {
  padding-top: 80px;
  position: relative;
}
.vl-footer-area4 .shape {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -2;
}

.vl-copyright-social4 a {
  display: inline-block;
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 50%;
  background-color: var(--vtc-bg-common-14);
  color: var(--vtc-text-title-1);
  font-size: 16px;
  margin-right: 3px;
  transition: all 0.4s;
}
.vl-copyright-social4 a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main8);
  color: var(--vtc-bg-white1);
}

.vl-footer-list a {
  transition: all 0.4s;
}
.vl-footer-list a:hover {
  transform: translateX(5px);
  transition: all 0.4s;
}

.vl-footer-area4 .black-logo {
  display: block;
}
.vl-footer-area4 .white-logo {
  display: none;
}

.dark-mode .vl-footer-area4 .black-logo {
  display: none;
}
.dark-mode .vl-footer-area4 .white-logo {
  display: block;
}
.dark-mode .single-contact .icon img {
  filter: brightness(40);
}

/*
::::::::::::::::::::::::::
 FOOTER AREA 4 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 FOOTER AREA 5 CSS
::::::::::::::::::::::::::
*/
.vl-copyright5._dv-top {
  border-top: 1px solid var(--vtc-border-3);
}

.vl-footer-thumb5 {
  width: 70px;
  margin-bottom: 16px;
}
.vl-footer-thumb5 img {
  width: 100%;
  border-radius: 8px;
  transition: all 0.4s;
}
.vl-footer-thumb5 a {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  height: 26px;
  width: 26px;
  text-align: center;
  line-height: 26px;
  color: var(--vtc-bg-white1);
  background: var(--vtc-bg-main10);
  margin-top: -13px;
  margin-left: -13px;
  border-radius: 4px;
  transition: all 0.4s;
  transform: scale(1.2);
  opacity: 0;
}
.vl-footer-thumb5:hover {
  transition: all 0.4s;
}
.vl-footer-thumb5:hover img {
  transition: all 0.4s;
  filter: grayscale(1);
}
.vl-footer-thumb5:hover a {
  transform: scale(1);
  transition: all 0.4s;
  opacity: 1;
}

.vl-footer-thumb-box {
  -moz-column-gap: 16px;
       column-gap: 16px;
}

.vl-footer-area5 {
  position: relative;
}
.vl-footer-area5 .shape {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -2;
}

.vl-copyright-social5 a {
  display: inline-block;
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.103);
  color: var(--vtc-bg-white1);
  font-size: 16px;
  margin-right: 3px;
  transition: all 0.4s;
}
.vl-copyright-social5 a:hover {
  transition: all 0.4s;
  background: var(--vtc-bg-main10);
  color: var(--vtc-bg-white1);
}

.vl-footer-list5 a {
  transition: all 0.4s;
}
.vl-footer-list5 a:hover {
  transform: translateX(5px);
  transition: all 0.4s;
  color: var(--vtc-bg-white1);
}

.vl-footer-contact5 .single-contact .icon img {
  filter: brightness(40);
}
.vl-footer-contact5 .single-contact .text a {
  transition: all 0.4s;
}
.vl-footer-contact5 .single-contact .text a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-white1);
}

/*
::::::::::::::::::::::::::
 FOOTER AREA 4 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 1 CSS
::::::::::::::::::::::::::
*/
.header1-top {
  border-bottom: 1px solid var(--vtc-border-1);
}

.header-contact-wedget a {
  position: relative;
}
.header-contact-wedget a img {
  transform: translateY(-2px);
}
.header-contact-wedget a:nth-last-child(1)::after {
  content: "";
  position: absolute;
  top: 0;
  left: -19px;
  height: 20px;
  width: 2px;
  background-color: var(--vtc-border-1);
}

.header-tranperent {
  position: absolute;
  top: 0;
  z-index: 999;
  width: 100%;
}

.header-contact-wedget {
  text-align: end;
}

@media screen and (min-width: 769px) {
  .header1-bg {
    padding: 20px 38px;
    border-radius: 0px 0px 8px 8px;
    position: relative;
    z-index: 999;
  }
  .header1-bg::after {
    content: "";
    position: absolute;
    left: 11px;
    top: 0;
    height: 100%;
    width: 98.3%;
    border-radius: 0px 0px 8px 8px;
    background-color: var(--vtc-text-white);
    z-index: -1;
  }
  .header1-bg::before {
    content: "";
    position: absolute;
    left: 11px;
    top: 0;
    height: 100%;
    width: 6px;
    background-color: var(--vtc-bg-main1);
    z-index: 0;
    border-radius: 0px 0px 0px 8px;
  }
}
.vl-header1-right {
  text-align: end;
  display: flex;
  align-items: center;
  justify-content: end;
}
.vl-header1-right .search-open-btn {
  background: none;
  border: none;
  font-size: var(--f-fs-font-20);
  margin-right: 36px;
  position: relative;
}
.vl-header1-right .search-open-btn::after {
  content: "";
  position: absolute;
  top: 5px;
  right: -16px;
  height: 20px;
  width: 2px;
  background-color: var(--vtc-border-1);
}

.header1-social-wedget a {
  display: inline-block;
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 50%;
  background-color: var(--vtc-bg-common-2);
  color: var(--vtc-text-title-1);
  font-size: 16px;
  margin-right: 3px;
  transition: all 0.4s;
}
.header1-social-wedget a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main1);
}

.header1-logo-white {
  display: none;
  display: none;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .dark-mode .header1-logo-white {
    display: inline-block;
  }
}
@media (max-width: 767px) {
  .dark-mode .header1-logo-white {
    display: inline-block;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .dark-mode .header1-logo-block {
    display: none;
  }
}
@media (max-width: 767px) {
  .dark-mode .header1-logo-block {
    display: none;
  }
}

/*
::::::::::::::::::::::::::
 HEADER AREA 1 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 2 CSS
::::::::::::::::::::::::::
*/
.header1-top {
  border-bottom: 1px solid var(--vtc-border-1);
}

.header-contact-wedget a {
  position: relative;
}
.header-contact-wedget a img {
  transform: translateY(-2px);
}
.header-contact-wedget a:nth-last-child(1)::after {
  content: "";
  position: absolute;
  top: 0;
  left: -19px;
  height: 20px;
  width: 2px;
  background-color: var(--vtc-border-1);
}

.header-tranperent {
  position: absolute;
  top: 0;
  z-index: 999;
  width: 100%;
}

.header-contact-wedget {
  text-align: end;
}

@media screen and (min-width: 769px) {
  .header2-bg {
    padding: 20px 38px;
    border-radius: 0px 0px 8px 8px;
    position: relative;
    z-index: 999;
  }
  .header2-bg::after {
    content: "";
    position: absolute;
    left: 11px;
    top: 0;
    height: 100%;
    width: 98.3%;
    border-radius: 0px 0px 8px 8px;
    background-color: var(--vtc-text-white);
    z-index: -1;
  }
  .header2-bg::before {
    content: "";
    position: absolute;
    left: 11px;
    top: 0;
    height: 100%;
    width: 6px;
    background-color: var(--vtc-bg-main4);
    z-index: 0;
    border-radius: 0px 0px 0px 8px;
  }
}
.vl-header1-right {
  text-align: end;
  display: flex;
  align-items: center;
  justify-content: end;
}
.vl-header1-right .search-open-btn {
  background: none;
  border: none;
  font-size: var(--f-fs-font-20);
  margin-right: 36px;
  position: relative;
}
.vl-header1-right .search-open-btn::after {
  content: "";
  position: absolute;
  top: 5px;
  right: -16px;
  height: 20px;
  width: 2px;
  background-color: var(--vtc-border-1);
}

.header2-social-wedget a {
  display: inline-block;
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 50%;
  background-color: var(--vtc-bg-common-6);
  color: var(--vtc-text-title-1);
  font-size: 16px;
  margin-right: 3px;
  transition: all 0.4s;
}
.header2-social-wedget a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main4);
  color: var(--vtc-bg-white1);
}

.header1-logo-white {
  display: none;
  display: none;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .dark-mode .header1-logo-white {
    display: inline-block;
  }
}
@media (max-width: 767px) {
  .dark-mode .header1-logo-white {
    display: inline-block;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .dark-mode .header1-logo-block {
    display: none;
  }
}
@media (max-width: 767px) {
  .dark-mode .header1-logo-block {
    display: none;
  }
}

/*
::::::::::::::::::::::::::
 HEADER AREA 2 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 3 CSS
::::::::::::::::::::::::::
*/
@media screen and (min-width: 769px) {
  .header3-bg {
    padding: 20px 38px;
    border-radius: 8px 8px 8px 8px;
    position: relative;
    z-index: 999;
    margin-top: 20px;
  }
  .header3-bg::after {
    content: "";
    position: absolute;
    left: 11px;
    top: 0;
    height: 100%;
    width: 98.3%;
    border-radius: 8px 8px 8px 8px;
    background-color: var(--vtc-text-white);
    z-index: -1;
  }
  .header3-bg::before {
    content: "";
    position: absolute;
    left: 11px;
    top: 0;
    height: 100%;
    width: 6px;
    background-color: var(--vtc-bg-main6);
    z-index: 0;
    border-radius: 8px 0px 0px 8px;
  }
}
.header3-social-wedget a {
  display: inline-block;
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 50%;
  background-color: var(--vtc-bg-common-6);
  color: var(--vtc-text-title-1);
  font-size: 16px;
  margin-right: 3px;
  transition: all 0.4s;
}
.header3-social-wedget a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main6);
  color: var(--vtc-bg-white1);
}

/*
::::::::::::::::::::::::::
 HEADER AREA 3 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 4 CSS
::::::::::::::::::::::::::
*/
.header1-top {
  border-bottom: 1px solid var(--vtc-border-1);
}

.header-contact-wedget a {
  position: relative;
}
.header-contact-wedget a img {
  transform: translateY(-2px);
}
.header-contact-wedget a:nth-last-child(1)::after {
  content: "";
  position: absolute;
  top: 0;
  left: -19px;
  height: 20px;
  width: 2px;
  background-color: var(--vtc-border-1);
}

.header-tranperent {
  position: absolute;
  top: 0;
  z-index: 999;
  width: 100%;
}

.header-contact-wedget {
  text-align: end;
}

@media screen and (min-width: 769px) {
  .header4-bg {
    padding: 20px 38px;
    border-radius: 0px 0px 8px 8px;
    position: relative;
    z-index: 999;
  }
  .header4-bg::after {
    content: "";
    position: absolute;
    left: 11px;
    top: 0;
    height: 100%;
    width: 98.3%;
    border-radius: 0px 0px 8px 8px;
    background-color: var(--vtc-text-white);
    z-index: -1;
  }
  .header4-bg::before {
    content: "";
    position: absolute;
    left: 11px;
    top: 0;
    height: 100%;
    width: 6px;
    background-color: var(--vtc-bg-main8);
    z-index: 0;
    border-radius: 0px 0px 0px 8px;
  }
}
.vl-header1-right {
  text-align: end;
  display: flex;
  align-items: center;
  justify-content: end;
}
.vl-header1-right .search-open-btn {
  background: none;
  border: none;
  font-size: var(--f-fs-font-20);
  margin-right: 36px;
  position: relative;
}
.vl-header1-right .search-open-btn::after {
  content: "";
  position: absolute;
  top: 5px;
  right: -16px;
  height: 20px;
  width: 2px;
  background-color: var(--vtc-border-1);
}

.header4-social-wedget a {
  display: inline-block;
  height: 36px;
  width: 36px;
  text-align: center;
  line-height: 36px;
  border-radius: 50%;
  background-color: var(--vtc-bg-common-6);
  color: var(--vtc-text-title-1);
  font-size: 16px;
  margin-right: 3px;
  transition: all 0.4s;
}
.header4-social-wedget a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main8);
  color: var(--vtc-bg-white1);
}

.header1-logo-white {
  display: none;
  display: none;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .dark-mode .header1-logo-white {
    display: inline-block;
  }
}
@media (max-width: 767px) {
  .dark-mode .header1-logo-white {
    display: inline-block;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .dark-mode .header1-logo-block {
    display: none;
  }
}
@media (max-width: 767px) {
  .dark-mode .header1-logo-block {
    display: none;
  }
}

/*
::::::::::::::::::::::::::
 HEADER AREA 4 CSS
::::::::::::::::::::::::::
*/
/*
::::::::::::::::::::::::::
 HEADER AREA 5 CSS
::::::::::::::::::::::::::
*/
@media screen and (min-width: 769px) {
  .header5-bg {
    padding: 20px 38px;
    border-radius: 8px 8px 8px 8px;
    position: relative;
    z-index: 999;
    margin-top: 20px;
  }
  .header5-bg::after {
    content: "";
    position: absolute;
    left: 11px;
    top: 0;
    height: 100%;
    width: 98.3%;
    border-radius: 8px 8px 8px 8px;
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
    z-index: -1;
  }
  .header5-bg::before {
    content: "";
    position: absolute;
    left: 11px;
    top: 0;
    height: 100%;
    width: 6px;
    background-color: var(--vtc-bg-white1);
    z-index: 0;
    border-radius: 8px 0px 0px 8px;
  }
  .vl-header5-right {
    text-align: end;
    display: flex;
    align-items: center;
    justify-content: end;
  }
  .vl-header5-right .search-open-btn {
    background: none;
    border: none;
    font-size: var(--f-fs-font-20);
    margin-right: 36px;
    position: relative;
    color: var(--vtc-bg-white1);
  }
  .vl-header5-right .search-open-btn::after {
    content: "";
    position: absolute;
    top: 5px;
    right: -16px;
    height: 20px;
    width: 2px;
    background-color: var(--vtc-border-1);
  }
  .header5-social-wedget a {
    display: inline-block;
    height: 36px;
    width: 36px;
    text-align: center;
    line-height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--vtc-bg-white1);
    font-size: 16px;
    margin-right: 3px;
    transition: all 0.4s;
  }
  .header5-social-wedget a:hover {
    transition: all 0.4s;
    color: var(--vtc-bg-white1);
    transform: translateY(-4px);
  }
}
/*
::::::::::::::::::::::::::
 HEADER AREA 5 CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  NAV MENU 1 CSS
 ::::::::::::::::::::::::::
 */
/*
 ::::::::::::::::::::::::::
  NAV MENU 1 CSS
 ::::::::::::::::::::::::::
 */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area1 {
    padding-top: 12px;
    background-color: var(--vtc-bg-white2);
    padding-bottom: 12px;
  }
}
@media (max-width: 767px) {
  .vl-header-area1 {
    padding-top: 12px;
    background-color: var(--vtc-bg-white2);
    padding-bottom: 12px;
  }
}
.vl-header-area1 .vl-main-menu ul {
  text-align: center;
}
.vl-header-area1 .vl-main-menu ul > li {
  display: inline-block;
  position: relative;
}
.vl-header-area1 .vl-main-menu ul > li .span-arrow {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
}
.vl-header-area1 .vl-main-menu ul > li > a {
  color: var(--vtc-text-title-2);
  font-size: var(--f-fs-font-18);
  display: inline-block;
  position: relative;
  transition: 0.4s;
  padding: 0 16px;
}
.vl-header-area1 .vl-main-menu ul > li > a.active {
  color: var(--vtc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li > a:hover {
  color: var(--vtc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li:hover a {
  color: var(--vtc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu {
  position: absolute;
  top: 201%;
  width: 220px;
  left: 0;
  background: #fff;
  padding: 12px 20px 8px;
  opacity: 0;
  visibility: hidden;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  transition: 0.4s;
  border-radius: 4px;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--vtc-bg-main1);
  top: 0;
  left: 0;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu.menu1 {
  top: 20% !important;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li {
  margin-right: 0;
  display: block;
  text-align: start;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li a {
  color: var(--ztc-text-text-2);
  display: inline-block;
  font-size: var(--f-fs-font-18);
  position: relative;
  z-index: 1;
  padding: 9px 0;
  font-weight: var(--f-fw-medium);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li a::after {
  position: absolute;
  content: "";
  height: 45px;
  width: 0px;
  transition: all 0.4s;
  left: -20px;
  top: 2px;
  right: auto;
  z-index: 1;
  background-color: var(--vtc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li a:hover::after {
  width: 2px;
  transition: all 0.4s;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li a:before {
  display: none;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li .sub-menu {
  left: 100%;
  top: 201%;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li:hover > a {
  color: var(--vtc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li:hover > a::after {
  width: 3px;
}
.vl-header-area1 .vl-main-menu ul > li .sub-menu li:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
}
.vl-header-area1 .vl-main-menu ul > li:hover a {
  color: var(--vtc-bg-main1);
}
.vl-header-area1 .vl-main-menu ul > li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
  transition: all 0.4s;
}
.vl-header-area1 .vl-main-menu-black ul li a {
  color: var(--ztc-text-text-1);
  opacity: 80%;
  padding: 0 20px;
}
.vl-header-area1 .vl-main-menu-black ul li:hover a {
  color: var(--vl-theme-orange);
}
.vl-header-area1 .vl-main-menu-black ul li .sub-menu li:hover > a {
  color: var(--vl-theme-orange);
}
.vl-header-area1 .vl-main-menu ul > li:hover .vl-mega-menu {
  opacity: 1;
  visibility: visible;
  transition: 0.3s;
  top: 201%;
  transform: scale(1);
}
.vl-header-area1 .vl-mega-menu {
  position: absolute;
  left: -256px;
  top: 100px;
  width: 1298px;
  background: #fff;
  padding: 25px;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
  top: 201.3%;
  transform: scale(1, 0);
  transform-origin: top;
  border-radius: 0px 0px 5px 5px;
}
.vl-header-area1 .vl-mega-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--vtc-bg-main1);
  top: 0;
  left: 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .vl-header-area1 .vl-mega-menu {
    left: -162px;
    width: 929px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .vl-header-area1 .vl-mega-menu {
    width: auto;
    opacity: 1;
    visibility: visible;
    transition: none;
    position: static;
    display: none;
    transform: scale(1);
  }
}
.vl-header-area1 .vl-home-thumb {
  position: relative;
  z-index: 1;
}
.vl-header-area1 .vl-home-thumb img {
  height: 320px;
  width: 100%;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area1 .vl-home-thumb img {
    -o-object-fit: contain;
       object-fit: contain;
  }
}
@media (max-width: 767px) {
  .vl-header-area1 .vl-home-thumb img {
    -o-object-fit: contain;
       object-fit: contain;
  }
}
.vl-header-area1 .vl-home-thumb .img1 {
  position: relative;
  z-index: 1;
}
.vl-header-area1 .vl-home-thumb .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  transition: all 0.4s;
  top: 0;
  background: var(--vtc-text-title-2);
  border-radius: 4px;
  transform: scale(0.5);
  visibility: hidden;
  opacity: 0;
}
.vl-header-area1 .vl-home-thumb .btn-area1 {
  position: absolute;
  top: 0;
  left: 0px;
  right: 0%;
  transition: all 0.6s;
  visibility: hidden;
  opacity: 0;
  z-index: 2;
}
.vl-header-area1 .vl-home-thumb .btn-area1 .vl-btn1 {
  position: relative;
  display: inline-block;
  padding: 18px 24px;
  border-radius: 8px;
  color: #2E0797 !important;
  background: var(--ztc-bg-bg-1);
  z-index: 1;
  font-family: var(--ztc-family-font1);
  font-size: var(--ztc-font-size-font-s20);
  line-height: 20px;
  font-weight: 700;
  transition: all 0.4s;
  width: 150px;
}
.vl-header-area1 .vl-home-thumb .btn-area1 .vl-btn1:hover {
  color: var(--ztc-text-text-1) !important;
  transition: all 0.4s;
}
.vl-header-area1 .vl-home-thumb .btn-area1 .vl-btn1:hover i {
  transform: rotate(0);
  transition: all 0.4s;
}
.vl-header-area1 .vl-home-thumb .btn-area1 .vl-btn1:hover::after {
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  width: 100%;
  left: 0;
}
.vl-header-area1 .vl-home-thumb .btn-area1 .vl-btn1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 10px;
  background: red;
  transition: all 0.4s;
  top: 0;
  left: 50%;
  border-radius: 8px;
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.vl-header-area1 .vl-home-thumb .btn-area1 .vl-btn1 i {
  margin-left: 4px;
  transform: rotate(-45deg);
  transition: all 0.4s;
}
.vl-header-area1 .vl-home-thumb a {
  font-size: var(--f-fs-font-18);
  line-height: 18px;
  font-weight: var(--f-fw-medium);
  color: var(--vtc-text-title-2) !important;
  transition: all 0.4s;
  display: inline-flex;
  padding-top: 16px;
  text-align: center;
  transition: all 0.4s;
}
.vl-header-area1 .vl-home-thumb a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main1);
}
.vl-header-area1 .vl-home-thumb:hover .btn-area1 {
  visibility: visible;
  opacity: 1;
  transition: all 0.6s;
  top: 23%;
}
.vl-header-area1 .vl-home-thumb:hover .img1::after {
  transform: scale(1);
  transition: all 0.4s;
  visibility: visible;
  opacity: 0.5;
}
.vl-header-area1.header-sticky {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  animation: 0.7s ease-in-out 0s normal none 1 running vlfadeInDown;
}
.vl-header-area1.header-sticky .header1-bg::after {
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.09);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area1.header-sticky .header1-bg {
    box-shadow: none;
  }
}
@media (max-width: 767px) {
  .vl-header-area1.header-sticky .header1-bg {
    box-shadow: none;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area1.header-sticky {
    background: var(--vtc-bg-white2);
    padding: 10px 0px 10px 0px;
  }
}
@media (max-width: 767px) {
  .vl-header-area1.header-sticky {
    background: var(--vtc-bg-white2);
    padding: 10px 0px 10px 0px;
  }
}

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.vlfadeInDown {
  animation: vlfadeInDown 1s ease-out forwards;
}

/*============= HEADER CSS AREA ENDS ===============*/
/*============= MOBILE MENU CSS AREA ===============*/
.vl-header-area1 .vl-header-action-item {
  float: right;
  padding: 6px;
  border-radius: 4px;
}
.vl-header-area1 .vl-header-action-item button {
  border: 1px solid var(--vtc-text-pera-1);
  outline: none;
  background: none;
  transition: all 0.4s;
  color: var(--vtc-text-title-1);
  font-size: var(--vtc-text-pera-1);
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 8px;
  font-size: var(--f-fs-font-22);
}

.vl-offcanvas {
  position: fixed;
  background: var(--vtc-bg-white2);
  width: 450px;
  z-index: 9999;
  right: 0;
  top: 0;
  padding: 50px 40px;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transform: translateX(100%);
  transition: 0.3s;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
}
@media only screen and (max-width: 450px) {
  .vl-offcanvas {
    width: 100%;
  }
}
.vl-offcanvas-open {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}
.vl-offcanvas-close-toggle {
  font-size: var(--f-fs-font-24);
  color: var(--vtc-text-title-1);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .vl-offcanvas-header {
    margin-bottom: 40px;
  }
}
.vl-offcanvas-title {
  font-size: 35px;
  color: var(--ztc-text-text-1);
}
.vl-offcanvas-info span a {
  display: block;
  color: var(--ztc-text-text-1);
  margin-bottom: 10px;
}
.vl-offcanvas-info span a i {
  margin: 0 4px 0 0;
}
.vl-offcanvas-sm-title {
  font-size: var(--ztc-font-size-font-s24);
  color: var(--ztc-text-text-1);
}
.vl-offcanvas-social a {
  display: inline-block;
  text-align: center;
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 40px;
  color: var(--vtc-text-title-1);
  border: 1px solid var(--ztc-text-text-1);
  font-size: 14px;
}
.vl-offcanvas-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 50;
  width: 100%;
  height: 100%;
  visibility: hidden;
  opacity: 0;
  transition: 0.45 easc-in-out;
  background: rgba(24, 24, 24, 0.4);
}
.vl-offcanvas-overlay-open {
  opacity: 0.7;
  visibility: visible;
}
.vl-offcanvas .vl-offcanvas-logo {
  height: 60px;
  width: 122px;
  -o-object-fit: contain;
     object-fit: contain;
}
.vl-offcanvas .vl-offcanvas-close button {
  border: none;
  background: none;
  outline: none;
  color: var(--vtc-text-title-1);
}

.vl-offcanvas-menu ul {
  list-style: none;
}
.vl-offcanvas-menu ul li {
  position: relative;
}
.vl-offcanvas-menu ul li a {
  padding: 8px 0;
  display: block;
  font-size: var(--f-fs-font-18);
  font-weight: var(--f-fw-medium);
  color: var(--vtc-text-title-1);
  transition: all 0.4s;
}
.vl-offcanvas-menu ul li a span {
  display: none;
}
.vl-offcanvas-menu ul li > a {
  border-bottom: none;
}
.vl-offcanvas-menu ul li.active > a {
  color: var(--vtc-text-title-1);
}
.vl-offcanvas-menu ul li.active > .vl-menu-close i {
  transform: rotate(90deg);
}
.vl-offcanvas-menu ul li .sub-menu {
  display: none;
  padding-left: 20px;
}

.vl-menu-close {
  position: absolute;
  right: 0;
  top: 7px;
  border: 1px solid var(--vtc-text-title-1);
  height: 30px;
  width: 30px;
  text-align: center;
  font-size: 14px;
  line-height: 25px;
  background: transparent;
  color: var(--vtc-text-title-1);
  border-radius: 4px;
}
.vl-menu-close i {
  transition: 0.3s;
}

.vl-home-thumb {
  text-align: center;
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 2 CSS
 ::::::::::::::::::::::::::
 */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area2 {
    padding-top: 12px;
    background-color: var(--vtc-bg-white4);
    padding-bottom: 12px;
  }
}
@media (max-width: 767px) {
  .vl-header-area2 {
    padding-top: 12px;
    background-color: var(--vtc-bg-white4);
    padding-bottom: 12px;
  }
}
.vl-header-area2 .vl-main-menu ul {
  text-align: center;
}
.vl-header-area2 .vl-main-menu ul > li {
  display: inline-block;
  position: relative;
}
.vl-header-area2 .vl-main-menu ul > li .span-arrow {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
}
.vl-header-area2 .vl-main-menu ul > li > a {
  color: var(--vtc-text-title-2);
  font-size: var(--f-fs-font-18);
  display: inline-block;
  position: relative;
  transition: 0.4s;
  padding: 0 16px;
}
.vl-header-area2 .vl-main-menu ul > li > a.active {
  color: var(--vtc-bg-main4);
}
.vl-header-area2 .vl-main-menu ul > li > a:hover {
  color: var(--vtc-bg-main4);
}
.vl-header-area2 .vl-main-menu ul > li:hover a {
  color: var(--vtc-bg-main4);
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu {
  position: absolute;
  top: 201%;
  width: 220px;
  left: 0;
  background: #fff;
  padding: 12px 20px 8px;
  opacity: 0;
  visibility: hidden;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  transition: 0.4s;
  border-radius: 4px;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--vtc-bg-main4);
  top: 0;
  left: 0;
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu.menu1 {
  top: 20% !important;
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu li {
  margin-right: 0;
  display: block;
  text-align: start;
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu li a {
  color: var(--ztc-text-text-2);
  display: inline-block;
  font-size: var(--f-fs-font-18);
  position: relative;
  z-index: 1;
  padding: 9px 0;
  font-weight: var(--f-fw-medium);
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu li a::after {
  position: absolute;
  content: "";
  height: 45px;
  width: 0px;
  transition: all 0.4s;
  left: -20px;
  top: 2px;
  right: auto;
  z-index: 1;
  background-color: var(--vtc-bg-main4);
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu li a:hover::after {
  width: 2px;
  transition: all 0.4s;
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu li a:before {
  display: none;
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu li .sub-menu {
  left: 100%;
  top: 201%;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu li:hover > a {
  color: var(--vtc-bg-main4);
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu li:hover > a::after {
  width: 3px;
}
.vl-header-area2 .vl-main-menu ul > li .sub-menu li:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
}
.vl-header-area2 .vl-main-menu ul > li:hover a {
  color: var(--vtc-bg-main4);
}
.vl-header-area2 .vl-main-menu ul > li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
  transition: all 0.4s;
}
.vl-header-area2 .vl-main-menu-black ul li a {
  color: var(--ztc-text-text-1);
  opacity: 80%;
  padding: 0 20px;
}
.vl-header-area2 .vl-main-menu-black ul li:hover a {
  color: var(--vl-theme-orange);
}
.vl-header-area2 .vl-main-menu-black ul li .sub-menu li:hover > a {
  color: var(--vl-theme-orange);
}
.vl-header-area2 .vl-main-menu ul > li:hover .vl-mega-menu {
  opacity: 1;
  visibility: visible;
  transition: 0.3s;
  top: 201%;
  transform: scale(1);
}
.vl-header-area2 .vl-mega-menu {
  position: absolute;
  left: -256px;
  top: 100px;
  width: 1298px;
  background: #fff;
  padding: 25px;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
  top: 201.3%;
  transform: scale(1, 0);
  transform-origin: top;
  border-radius: 0px 0px 5px 5px;
}
.vl-header-area2 .vl-mega-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--vtc-bg-main4);
  top: 0;
  left: 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .vl-header-area2 .vl-mega-menu {
    left: -162px;
    width: 929px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .vl-header-area2 .vl-mega-menu {
    width: auto;
    opacity: 1;
    visibility: visible;
    transition: none;
    position: static;
    display: none;
    transform: scale(1);
  }
}
.vl-header-area2 .vl-home-thumb {
  position: relative;
  z-index: 1;
}
.vl-header-area2 .vl-home-thumb img {
  height: 320px;
  width: 100%;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area2 .vl-home-thumb img {
    -o-object-fit: contain;
       object-fit: contain;
  }
}
@media (max-width: 767px) {
  .vl-header-area2 .vl-home-thumb img {
    -o-object-fit: contain;
       object-fit: contain;
  }
}
.vl-header-area2 .vl-home-thumb .img1 {
  position: relative;
  z-index: 1;
}
.vl-header-area2 .vl-home-thumb .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  transition: all 0.4s;
  top: 0;
  background: var(--vtc-text-title-1);
  border-radius: 4px;
  transform: scale(0.5);
  visibility: hidden;
  opacity: 0;
}
.vl-header-area2 .vl-home-thumb .btn-area1 {
  position: absolute;
  top: 0;
  left: 0px;
  right: 0%;
  transition: all 0.6s;
  visibility: hidden;
  opacity: 0;
  z-index: 2;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area2 .vl-home-thumb .btn-area1 {
    left: 25%;
    right: 25%;
  }
}
.vl-header-area2 .vl-home-thumb .btn-area1 .vl-btn1 {
  position: relative;
  display: inline-block;
  padding: 18px 24px;
  border-radius: 8px;
  color: #2E0797 !important;
  z-index: 1;
  line-height: 20px;
  font-weight: 700;
  transition: all 0.4s;
  width: 150px;
}
.vl-header-area2 .vl-home-thumb .btn-area1 .vl-btn1:hover {
  transition: all 0.4s;
}
.vl-header-area2 .vl-home-thumb .btn-area1 .vl-btn1:hover i {
  transform: rotate(0);
  transition: all 0.4s;
}
.vl-header-area2 .vl-home-thumb .btn-area1 .vl-btn1:hover::after {
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
  width: 100%;
  left: 0;
}
.vl-header-area2 .vl-home-thumb .btn-area1 .vl-btn1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 10px;
  transition: all 0.4s;
  top: 0;
  left: 50%;
  border-radius: 8px;
  z-index: -1;
  visibility: hidden;
  opacity: 0;
}
.vl-header-area2 .vl-home-thumb .btn-area1 .vl-btn1 i {
  margin-left: 4px;
  transform: rotate(-45deg);
  transition: all 0.4s;
}
.vl-header-area2 .vl-home-thumb a {
  font-size: var(--f-fs-font-18);
  line-height: 18px;
  font-weight: var(--f-fw-medium);
  color: var(--vtc-text-title-2) !important;
  transition: all 0.4s;
  display: inline-flex;
  padding-top: 16px;
  text-align: center;
  transition: all 0.4s;
}
.vl-header-area2 .vl-home-thumb a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main4);
}
.vl-header-area2 .vl-home-thumb:hover .btn-area1 {
  visibility: visible;
  opacity: 1;
  transition: all 0.6s;
  top: 23%;
}
.vl-header-area2 .vl-home-thumb:hover .img1::after {
  transform: scale(1);
  transition: all 0.4s;
  visibility: visible;
  opacity: 0.5;
}
.vl-header-area2.header-sticky {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  animation: 0.7s ease-in-out 0s normal none 1 running vlfadeInDown;
}
.vl-header-area2.header-sticky .header2-bg::after {
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.09);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area2.header-sticky .header2-bg {
    box-shadow: none;
  }
}
@media (max-width: 767px) {
  .vl-header-area2.header-sticky .header2-bg {
    box-shadow: none;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area2.header-sticky {
    background: var(--vtc-bg-white4);
    padding: 10px 0px 10px 0px;
  }
}
@media (max-width: 767px) {
  .vl-header-area2.header-sticky {
    background: var(--vtc-bg-white4);
    padding: 10px 0px 10px 0px;
  }
}

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.vlfadeInDown {
  animation: vlfadeInDown 1s ease-out forwards;
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 2 CSS
 ::::::::::::::::::::::::::
 */
.vl-header-area2 .vl-header-action-item {
  float: right;
  padding: 6px;
  border-radius: 4px;
}
.vl-header-area2 .vl-header-action-item button {
  border: 1px solid var(--vtc-text-pera-1);
  outline: none;
  background: none;
  transition: all 0.4s;
  color: var(--vtc-text-title-1);
  font-size: var(--vtc-text-pera-1);
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 8px;
  font-size: var(--f-fs-font-22);
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 3 CSS
 ::::::::::::::::::::::::::
 */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area3 {
    padding-top: 12px;
    background-color: var(--vtc-bg-white2);
    padding-bottom: 12px;
  }
}
@media (max-width: 767px) {
  .vl-header-area3 {
    padding-top: 12px;
    background-color: var(--vtc-bg-white2);
    padding-bottom: 12px;
  }
}
.vl-header-area3 .vl-main-menu ul {
  text-align: center;
}
.vl-header-area3 .vl-main-menu ul > li {
  display: inline-block;
  position: relative;
}
.vl-header-area3 .vl-main-menu ul > li .span-arrow {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
}
.vl-header-area3 .vl-main-menu ul > li > a {
  color: var(--vtc-text-title-2);
  font-size: var(--f-fs-font-18);
  display: inline-block;
  position: relative;
  transition: 0.4s;
  padding: 0 16px;
}
.vl-header-area3 .vl-main-menu ul > li > a.active {
  color: var(--vtc-bg-main6);
}
.vl-header-area3 .vl-main-menu ul > li > a:hover {
  color: var(--vtc-bg-main6);
}
.vl-header-area3 .vl-main-menu ul > li:hover a {
  color: var(--vtc-bg-main6);
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu {
  position: absolute;
  top: 201%;
  width: 220px;
  left: 0;
  background: #fff;
  padding: 12px 20px 8px;
  opacity: 0;
  visibility: hidden;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  transition: 0.4s;
  border-radius: 4px;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--vtc-bg-main6);
  top: 0;
  left: 0;
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu.menu1 {
  top: 20% !important;
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu li {
  margin-right: 0;
  display: block;
  text-align: start;
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu li a {
  color: var(--ztc-text-text-2);
  display: inline-block;
  font-size: var(--f-fs-font-18);
  position: relative;
  z-index: 1;
  padding: 9px 0;
  font-weight: var(--f-fw-medium);
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu li a::after {
  position: absolute;
  content: "";
  height: 45px;
  width: 0px;
  transition: all 0.4s;
  left: -20px;
  top: 2px;
  right: auto;
  z-index: 1;
  background-color: var(--vtc-bg-main6);
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu li a:hover::after {
  width: 2px;
  transition: all 0.4s;
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu li a:before {
  display: none;
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu li .sub-menu {
  left: 100%;
  top: 201%;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu li:hover > a {
  color: var(--vtc-bg-main6);
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu li:hover > a::after {
  width: 3px;
}
.vl-header-area3 .vl-main-menu ul > li .sub-menu li:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
}
.vl-header-area3 .vl-main-menu ul > li:hover a {
  color: var(--vtc-bg-main6);
}
.vl-header-area3 .vl-main-menu ul > li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
  transition: all 0.4s;
}
.vl-header-area3 .vl-main-menu-black ul li a {
  color: var(--ztc-text-text-1);
  opacity: 80%;
  padding: 0 20px;
}
.vl-header-area3 .vl-main-menu-black ul li:hover a {
  color: var(--vl-theme-orange);
}
.vl-header-area3 .vl-main-menu-black ul li .sub-menu li:hover > a {
  color: var(--vl-theme-orange);
}
.vl-header-area3 .vl-main-menu ul > li:hover .vl-mega-menu {
  opacity: 1;
  visibility: visible;
  transition: 0.3s;
  top: 201%;
  transform: scale(1);
}
.vl-header-area3 .vl-mega-menu {
  position: absolute;
  left: -256px;
  top: 100px;
  width: 1298px;
  background: #fff;
  padding: 25px;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
  top: 201.3%;
  transform: scale(1, 0);
  transform-origin: top;
  border-radius: 0px 0px 5px 5px;
}
.vl-header-area3 .vl-mega-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--vtc-bg-main6);
  top: 0;
  left: 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .vl-header-area3 .vl-mega-menu {
    left: -162px;
    width: 929px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .vl-header-area3 .vl-mega-menu {
    width: auto;
    opacity: 1;
    visibility: visible;
    transition: none;
    position: static;
    display: none;
    transform: scale(1);
  }
}
.vl-header-area3 .vl-home-thumb {
  position: relative;
  z-index: 1;
}
.vl-header-area3 .vl-home-thumb img {
  height: 320px;
  width: 100%;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area3 .vl-home-thumb img {
    -o-object-fit: cover;
       object-fit: cover;
  }
}
.vl-header-area3 .vl-home-thumb .img1 {
  position: relative;
  z-index: 1;
}
.vl-header-area3 .vl-home-thumb .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  transition: all 0.4s;
  top: 0;
  background: var(--vtc-text-title-1);
  border-radius: 4px;
  transform: scale(0.5);
  visibility: hidden;
  opacity: 0;
}
.vl-header-area3 .vl-home-thumb .btn-area1 {
  position: absolute;
  top: 0;
  left: 0px;
  right: 0%;
  transition: all 0.6s;
  visibility: hidden;
  opacity: 0;
  z-index: 2;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area3 .vl-home-thumb .btn-area1 {
    left: 25%;
    right: 25%;
  }
}
.vl-header-area3 .vl-home-thumb .btn-area1 a {
  color: var(--vtc-bg-white1) !important;
}
.vl-header-area3 .vl-home-thumb a {
  font-size: var(--f-fs-font-18);
  line-height: 18px;
  font-weight: var(--f-fw-medium);
  color: var(--vtc-text-title-2) !important;
  transition: all 0.4s;
  display: inline-flex;
  padding-top: 16px;
  text-align: center;
  transition: all 0.4s;
}
.vl-header-area3 .vl-home-thumb a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main6);
}
.vl-header-area3 .vl-home-thumb:hover .btn-area1 {
  visibility: visible;
  opacity: 1;
  transition: all 0.6s;
  top: 28%;
}
.vl-header-area3 .vl-home-thumb:hover .img1::after {
  transform: scale(1);
  transition: all 0.4s;
  visibility: visible;
  opacity: 0.5;
}
.vl-header-area3.header-sticky {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  animation: 0.7s ease-in-out 0s normal none 1 running vlfadeInDown;
  background-color: var(--vtc-bg-white1);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.09);
}
.vl-header-area3.header-sticky .header3-bg {
  margin-top: 0;
  padding: 16px 0px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area3.header-sticky .header3-bg {
    box-shadow: none;
  }
}
@media (max-width: 767px) {
  .vl-header-area3.header-sticky .header3-bg {
    box-shadow: none;
  }
}
.vl-header-area3.header-sticky .header3-bg::before {
  display: none;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area3.header-sticky {
    background: var(--vtc-bg-white2);
    padding: 10px 0px 10px 0px;
  }
}
@media (max-width: 767px) {
  .vl-header-area3.header-sticky {
    background: var(--vtc-bg-white2);
    padding: 10px 0px 10px 0px;
  }
}

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.vlfadeInDown {
  animation: vlfadeInDown 1s ease-out forwards;
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 3 CSS
 ::::::::::::::::::::::::::
 */
.vl-header-area3 .vl-header-action-item {
  float: right;
  padding: 6px;
  border-radius: 4px;
}
.vl-header-area3 .vl-header-action-item button {
  border: 1px solid var(--vtc-text-pera-1);
  outline: none;
  background: none;
  transition: all 0.4s;
  color: var(--vtc-text-title-1);
  font-size: var(--vtc-text-pera-1);
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 8px;
  font-size: var(--f-fs-font-22);
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 4 CSS
 ::::::::::::::::::::::::::
 */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area4 {
    padding-top: 12px;
    background-color: var(--vtc-bg-white6);
    padding-bottom: 12px;
  }
}
@media (max-width: 767px) {
  .vl-header-area4 {
    padding-top: 12px;
    background-color: var(--vtc-bg-white6);
    padding-bottom: 12px;
  }
}
.vl-header-area4 .vl-main-menu ul {
  text-align: center;
}
.vl-header-area4 .vl-main-menu ul > li {
  display: inline-block;
  position: relative;
}
.vl-header-area4 .vl-main-menu ul > li .span-arrow {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
}
.vl-header-area4 .vl-main-menu ul > li > a {
  color: var(--vtc-text-title-2);
  font-size: var(--f-fs-font-18);
  display: inline-block;
  position: relative;
  transition: 0.4s;
  padding: 0 16px;
}
.vl-header-area4 .vl-main-menu ul > li > a.active {
  color: var(--vtc-bg-main8);
}
.vl-header-area4 .vl-main-menu ul > li > a:hover {
  color: var(--vtc-bg-main8);
}
.vl-header-area4 .vl-main-menu ul > li:hover a {
  color: var(--vtc-bg-main8);
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu {
  position: absolute;
  top: 201%;
  width: 220px;
  left: 0;
  background: #fff;
  padding: 12px 20px 8px;
  opacity: 0;
  visibility: hidden;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  transition: 0.4s;
  border-radius: 4px;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--vtc-bg-main8);
  top: 0;
  left: 0;
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu.menu1 {
  top: 20% !important;
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu li {
  margin-right: 0;
  display: block;
  text-align: start;
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu li a {
  color: var(--ztc-text-text-2);
  display: inline-block;
  font-size: var(--f-fs-font-18);
  position: relative;
  z-index: 1;
  padding: 9px 0;
  font-weight: var(--f-fw-medium);
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu li a::after {
  position: absolute;
  content: "";
  height: 45px;
  width: 0px;
  transition: all 0.4s;
  left: -20px;
  top: 2px;
  right: auto;
  z-index: 1;
  background-color: var(--vtc-bg-main8);
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu li a:hover::after {
  width: 2px;
  transition: all 0.4s;
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu li a:before {
  display: none;
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu li .sub-menu {
  left: 100%;
  top: 201%;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu li:hover > a {
  color: var(--vtc-bg-main8);
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu li:hover > a::after {
  width: 3px;
}
.vl-header-area4 .vl-main-menu ul > li .sub-menu li:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
}
.vl-header-area4 .vl-main-menu ul > li:hover a {
  color: var(--vtc-bg-main8);
}
.vl-header-area4 .vl-main-menu ul > li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
  transition: all 0.4s;
}
.vl-header-area4 .vl-main-menu-black ul li a {
  color: var(--ztc-text-text-1);
  opacity: 80%;
  padding: 0 20px;
}
.vl-header-area4 .vl-main-menu-black ul li:hover a {
  color: var(--vl-theme-orange);
}
.vl-header-area4 .vl-main-menu-black ul li .sub-menu li:hover > a {
  color: var(--vl-theme-orange);
}
.vl-header-area4 .vl-main-menu ul > li:hover .vl-mega-menu {
  opacity: 1;
  visibility: visible;
  transition: 0.3s;
  top: 201%;
  transform: scale(1);
}
.vl-header-area4 .vl-mega-menu {
  position: absolute;
  left: -256px;
  top: 100px;
  width: 1298px;
  background: #fff;
  padding: 25px;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
  top: 201.3%;
  transform: scale(1, 0);
  transform-origin: top;
  border-radius: 0px 0px 5px 5px;
}
.vl-header-area4 .vl-mega-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--vtc-bg-main8);
  top: 0;
  left: 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .vl-header-area4 .vl-mega-menu {
    left: -162px;
    width: 929px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .vl-header-area4 .vl-mega-menu {
    width: auto;
    opacity: 1;
    visibility: visible;
    transition: none;
    position: static;
    display: none;
    transform: scale(1);
  }
}
.vl-header-area4 .vl-home-thumb {
  position: relative;
  z-index: 1;
}
.vl-header-area4 .vl-home-thumb img {
  height: 320px;
  width: 100%;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area4 .vl-home-thumb img {
    -o-object-fit: contain;
       object-fit: contain;
  }
}
@media (max-width: 767px) {
  .vl-header-area4 .vl-home-thumb img {
    -o-object-fit: contain;
       object-fit: contain;
  }
}
.vl-header-area4 .vl-home-thumb .img1 {
  position: relative;
  z-index: 1;
}
.vl-header-area4 .vl-home-thumb .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  transition: all 0.4s;
  top: 0;
  background: var(--vtc-text-title-1);
  border-radius: 4px;
  transform: scale(0.5);
  visibility: hidden;
  opacity: 0;
}
.vl-header-area4 .vl-home-thumb .btn-area1 {
  position: absolute;
  top: 0;
  left: 0px;
  right: 0%;
  transition: all 0.6s;
  visibility: hidden;
  opacity: 0;
  z-index: 2;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area4 .vl-home-thumb .btn-area1 {
    left: 25%;
    right: 25%;
  }
}
.vl-header-area4 .vl-home-thumb .btn-area1 .theme-btn5 {
  margin-top: 16px;
}
.vl-header-area4 .vl-home-thumb .btn-area1 .theme-btn5 span {
  color: #fff;
}
.vl-header-area4 .vl-home-thumb .btn-area1 .theme-btn5 img {
  height: 16px;
  border: none;
}
.vl-header-area4 .vl-home-thumb a {
  font-size: var(--f-fs-font-18);
  line-height: 18px;
  font-weight: var(--f-fw-medium);
  color: var(--vtc-text-title-2) !important;
  transition: all 0.4s;
  display: inline-flex;
  padding-top: 16px;
  text-align: center;
  transition: all 0.4s;
}
.vl-header-area4 .vl-home-thumb a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main8);
}
.vl-header-area4 .vl-home-thumb:hover .btn-area1 {
  visibility: visible;
  opacity: 1;
  transition: all 0.6s;
  top: 23%;
}
.vl-header-area4 .vl-home-thumb:hover .img1::after {
  transform: scale(1);
  transition: all 0.4s;
  visibility: visible;
  opacity: 0.5;
}
.vl-header-area4.header-sticky {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  animation: 0.7s ease-in-out 0s normal none 1 running vlfadeInDown;
}
.vl-header-area4.header-sticky .header4-bg::after {
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.09);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area4.header-sticky .header4-bg {
    box-shadow: none;
  }
}
@media (max-width: 767px) {
  .vl-header-area4.header-sticky .header4-bg {
    box-shadow: none;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area4.header-sticky {
    background: var(--vtc-bg-white6);
    padding: 10px 0px 10px 0px;
  }
}
@media (max-width: 767px) {
  .vl-header-area4.header-sticky {
    background: var(--vtc-bg-white6);
    padding: 10px 0px 10px 0px;
  }
}

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.vlfadeInDown {
  animation: vlfadeInDown 1s ease-out forwards;
}

.vl-header-area4 .vl-header-action-item {
  float: right;
  padding: 6px;
  border-radius: 4px;
}
.vl-header-area4 .vl-header-action-item button {
  border: 1px solid var(--vtc-text-pera-1);
  outline: none;
  background: none;
  transition: all 0.4s;
  color: var(--vtc-text-title-1);
  font-size: var(--vtc-text-pera-1);
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 8px;
  font-size: var(--f-fs-font-22);
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 4 CSS
 ::::::::::::::::::::::::::
 */
/*
 ::::::::::::::::::::::::::
  NAV MENU 3 CSS
 ::::::::::::::::::::::::::
 */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area5 {
    padding-top: 12px;
    background-color: var(--vtc-bg-white8);
    padding-bottom: 12px;
  }
}
@media (max-width: 767px) {
  .vl-header-area5 {
    padding-top: 12px;
    background-color: var(--vtc-bg-white8);
    padding-bottom: 12px;
  }
}
.vl-header-area5 .vl-main-menu ul {
  text-align: center;
}
.vl-header-area5 .vl-main-menu ul > li {
  display: inline-block;
  position: relative;
}
.vl-header-area5 .vl-main-menu ul > li .span-arrow {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
}
.vl-header-area5 .vl-main-menu ul > li > a {
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  display: inline-block;
  position: relative;
  transition: 0.4s;
  padding: 0 16px;
}
.vl-header-area5 .vl-main-menu ul > li > a:hover {
  color: var(--vtc-bg-white1);
}
.vl-header-area5 .vl-main-menu ul > li:hover a {
  color: var(--vtc-bg-white1);
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu {
  position: absolute;
  top: 201%;
  width: 220px;
  left: 0;
  background: #fff;
  padding: 12px 20px 8px;
  opacity: 0;
  visibility: hidden;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  transition: 0.4s;
  border-radius: 4px;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--vtc-bg-main12);
  top: 0;
  left: 0;
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu.menu1 {
  top: 20% !important;
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu li {
  margin-right: 0;
  display: block;
  text-align: start;
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu li a {
  color: var(--ztc-text-text-2);
  display: inline-block;
  font-size: var(--f-fs-font-18);
  position: relative;
  z-index: 1;
  padding: 9px 0;
  font-weight: var(--f-fw-medium);
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu li a::after {
  position: absolute;
  content: "";
  height: 45px;
  width: 0px;
  transition: all 0.4s;
  left: -20px;
  top: 2px;
  right: auto;
  z-index: 1;
  background-color: var(--vtc-bg-main12);
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu li a:hover::after {
  width: 2px;
  transition: all 0.4s;
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu li a:before {
  display: none;
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu li .sub-menu {
  left: 100%;
  top: 201%;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s;
  transform-origin: top;
  transform: scale(1, 0);
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu li:hover > a {
  color: var(--vtc-bg-main12);
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu li:hover > a::after {
  width: 3px;
}
.vl-header-area5 .vl-main-menu ul > li .sub-menu li:hover > .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
}
.vl-header-area5 .vl-main-menu ul > li:hover a {
  color: var(--vtc-bg-white1);
}
.vl-header-area5 .vl-main-menu ul > li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  top: 201%;
  transform: scale(1);
  transition: all 0.4s;
}
.vl-header-area5 .vl-main-menu-black ul li a {
  color: var(--ztc-text-text-1);
  opacity: 80%;
  padding: 0 20px;
}
.vl-header-area5 .vl-main-menu-black ul li:hover a {
  color: var(--vl-theme-orange);
}
.vl-header-area5 .vl-main-menu-black ul li .sub-menu li:hover > a {
  color: var(--vl-theme-orange);
}
.vl-header-area5 .vl-main-menu ul > li:hover .vl-mega-menu {
  opacity: 1;
  visibility: visible;
  transition: 0.3s;
  top: 201%;
  transform: scale(1);
}
.vl-header-area5 .vl-mega-menu {
  position: absolute;
  left: -256px;
  top: 100px;
  width: 1298px;
  background: #fff;
  padding: 25px;
  box-shadow: 0px 20px 30px rgba(1, 15, 28, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
  top: 201.3%;
  transform: scale(1, 0);
  transform-origin: top;
  border-radius: 0px 0px 5px 5px;
}
.vl-header-area5 .vl-mega-menu::after {
  content: "";
  position: absolute;
  top: 0;
  height: 2px;
  width: 100%;
  background-color: var(--vtc-bg-main12);
  top: 0;
  left: 0;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .vl-header-area5 .vl-mega-menu {
    left: -162px;
    width: 929px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 767px) {
  .vl-header-area5 .vl-mega-menu {
    width: auto;
    opacity: 1;
    visibility: visible;
    transition: none;
    position: static;
    display: none;
    transform: scale(1);
  }
}
.vl-header-area5 .vl-home-thumb {
  position: relative;
  z-index: 1;
}
.vl-header-area5 .vl-home-thumb img {
  height: 320px;
  width: 100%;
  border-radius: 4px;
  border: 1px solid #E5E7EB;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area5 .vl-home-thumb img {
    -o-object-fit: contain;
       object-fit: contain;
  }
}
@media (max-width: 767px) {
  .vl-header-area5 .vl-home-thumb img {
    -o-object-fit: contain;
       object-fit: contain;
  }
}
.vl-header-area5 .vl-home-thumb .img1 {
  position: relative;
  z-index: 1;
}
.vl-header-area5 .vl-home-thumb .img1::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  transition: all 0.4s;
  top: 0;
  background: var(--vtc-text-title-1);
  border-radius: 4px;
  transform: scale(0.5);
  visibility: hidden;
  opacity: 0;
}
.vl-header-area5 .vl-home-thumb .btn-area1 {
  position: absolute;
  top: 0;
  left: 0px;
  right: 0%;
  transition: all 0.6s;
  visibility: hidden;
  opacity: 0;
  z-index: 2;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area5 .vl-home-thumb .btn-area1 {
    left: 25%;
    right: 25%;
  }
}
.vl-header-area5 .vl-home-thumb .btn-area1 a {
  color: var(--vtc-bg-white1) !important;
  margin-top: 0px !important;
}
.vl-header-area5 .vl-home-thumb a {
  font-size: var(--f-fs-font-18);
  line-height: 18px;
  font-weight: var(--f-fw-medium);
  color: var(--vtc-text-title-2) !important;
  transition: all 0.4s;
  display: inline-flex;
  text-align: center;
  transition: all 0.4s;
  margin-top: 16px;
}
.vl-header-area5 .vl-home-thumb a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main12);
}
.vl-header-area5 .vl-home-thumb:hover .btn-area1 {
  visibility: visible;
  opacity: 1;
  transition: all 0.6s;
  top: 28%;
}
.vl-header-area5 .vl-home-thumb:hover .img1::after {
  transform: scale(1);
  transition: all 0.4s;
  visibility: visible;
  opacity: 0.5;
}
.vl-header-area5.header-sticky {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  animation: 0.7s ease-in-out 0s normal none 1 running vlfadeInDown;
  background-color: var(--vtc-bg-main12);
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.09);
}
.vl-header-area5.header-sticky .header5-bg {
  margin-top: 0;
  padding: 16px 0px;
  background: none;
}
.vl-header-area5.header-sticky .header5-bg::after {
  display: none;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area5.header-sticky .header5-bg {
    box-shadow: none;
  }
}
@media (max-width: 767px) {
  .vl-header-area5.header-sticky .header5-bg {
    box-shadow: none;
  }
}
.vl-header-area5.header-sticky .header5-bg::before {
  display: none;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area5.header-sticky {
    background: var(--vtc-bg-white8);
    padding: 0px 0px 10px 0px;
  }
}
@media (max-width: 767px) {
  .vl-header-area5.header-sticky {
    background: var(--vtc-bg-white8);
    padding: 0px 0px 10px 0px;
  }
}

@keyframes vlfadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.vlfadeInDown {
  animation: vlfadeInDown 1s ease-out forwards;
}

.vl-header-area5 .vl-header-action-item {
  float: right;
  padding: 6px;
  border-radius: 4px;
}
.vl-header-area5 .vl-header-action-item button {
  border: 1px solid var(--vtc-text-pera-1);
  outline: none;
  background: none;
  transition: all 0.4s;
  color: var(--vtc-text-title-1);
  font-size: var(--vtc-text-pera-1);
  height: 50px;
  width: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 8px;
  font-size: var(--f-fs-font-22);
}

.vl-header-area5 .black-logo {
  display: none;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area5 .black-logo {
    display: block;
  }
}
@media (max-width: 767px) {
  .vl-header-area5 .black-logo {
    display: block;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vl-header-area5 .white-logo {
    display: none;
  }
}
@media (max-width: 767px) {
  .vl-header-area5 .white-logo {
    display: none;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .dark-mode .white-logo {
    display: block;
  }
}
@media (max-width: 767px) {
  .dark-mode .white-logo {
    display: block;
  }
}
.dark-mode .black-logo {
  display: none;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .dark-mode .black-logo {
    display: none;
  }
}
@media (max-width: 767px) {
  .dark-mode .black-logo {
    display: none;
  }
}

/*
 ::::::::::::::::::::::::::
  NAV MENU 3 CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 ABOUT AREA CSS
::::::::::::::::::::::::::
*/
.images-all {
  height: 510px;
}
.images-all .shape1 {
  position: absolute;
  top: 11px;
  left: -120px;
  z-index: 4;
}
.images-all .shape2 {
  bottom: 30px;
  position: absolute;
  right: 0;
  z-index: 4;
}
.images-all .shape3 {
  position: absolute;
  top: -30px;
  right: 132px;
  z-index: -1;
}

.about1-list ul li {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
  padding-top: 16px;
  display: flex;
  align-items: center;
}
.about1-list ul li span {
  display: inline-block;
  background-color: var(--vtc-bg-main1);
  height: 20px;
  width: 20px;
  line-height: 20px;
  text-align: center;
  font-size: var(--vtc-text-title-1);
  border-radius: 50%;
  font-size: 12px;
  margin-right: 8px;
}

.about2-images-all {
  height: 664px;
  position: relative;
  text-align: start;
}
@media (max-width: 767px) {
  .about2-images-all {
    height: 550px;
  }
}
.about2-images-all .main-image {
  position: relative;
  z-index: 2;
  margin-right: 140px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about2-images-all .main-image {
    margin-right: 0px;
  }
}
@media (max-width: 767px) {
  .about2-images-all .main-image {
    margin-right: 0px;
  }
}
.about2-images-all .main-shape {
  position: absolute;
  left: 0;
  top: -100px;
  z-index: 1;
  transform: scale(1.4);
}
.about2-images-all .shape1 {
  position: absolute;
  bottom: 10px;
  right: 20px;
  z-index: 3;
}
@media (max-width: 767px) {
  .about2-images-all .shape1 {
    right: 0;
  }
}
.about2-images-all .shape2 {
  position: absolute;
  top: -40px;
  left: -35px;
}
.about2-images-all .circle-shape {
  position: absolute;
  bottom: 100px;
  left: 20px;
  z-index: 4;
}
.about2-images-all .circle-shape .circle-shape-arrow {
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 5;
  margin-top: -23px;
  margin-left: -18px;
}

.about3-icon-box {
  padding: 20px 40px 20px 20px;
  background-color: var(--vtc-bg-common-5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  margin-top: 30px;
  transition: all 0.4s;
}
.about3-icon-box .icon {
  background-color: var(--vtc-bg-common-7);
  height: 80px;
  width: 80px;
  text-align: center;
  line-height: 80px;
  border-radius: 8px;
  margin-right: 20px;
}
.about3-icon-box .icon img {
  transition: all 0.4s;
}
.about3-icon-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.about3-icon-box:hover .icon {
  transition: all 0.4s;
}
.about3-icon-box:hover .icon img {
  transition: all 0.4s;
  transform: rotateY(180deg);
}

.about2-shape-white {
  display: none;
}

.dark-mode .about2-shape-white {
  display: block;
}
.dark-mode .about2-shape-black {
  display: none;
}

.about3 .images {
  height: 536px;
  position: relative;
  text-align: end;
  margin-right: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about3 .images {
    margin-right: 0;
    margin-bottom: 30px;
  }
}
@media (max-width: 767px) {
  .about3 .images {
    margin-right: 0;
    margin-bottom: 30px;
    height: 420px;
  }
}
@media (max-width: 767px) {
  .about3 .images .image2 {
    width: 200px;
  }
}
.about3 .images .image1 {
  position: absolute;
  bottom: 0;
  left: 0;
}
@media (max-width: 767px) {
  .about3 .images .image1 {
    width: 200px;
    left: auto;
    right: 30px;
  }
}
.about3 .images .shape {
  position: absolute;
  top: 30px;
  left: 60px;
}
@media (max-width: 767px) {
  .about3 .images .shape {
    left: auto;
    right: 30px;
  }
}
.about3 .images2 {
  height: 536px;
  position: relative;
  text-align: start;
  margin-left: 20px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about3 .images2 {
    margin-left: 0;
    margin-bottom: 30px;
    margin-top: 40px;
  }
}
@media (max-width: 767px) {
  .about3 .images2 {
    margin-left: 0;
    margin-bottom: 30px;
    margin-top: 40px;
    height: 420px;
  }
}
@media (max-width: 767px) {
  .about3 .images2 .image1 {
    width: 200px;
  }
}
.about3 .images2 .image2 {
  position: absolute;
  bottom: 0;
  right: 0;
}
@media (max-width: 767px) {
  .about3 .images2 .image2 {
    width: 200px;
  }
}
.about3 .images2 .shape {
  position: absolute;
  top: 6px;
  left: 257px;
}
.about3 .images2 .circle-shape {
  position: absolute;
  bottom: 40px;
  left: 220px;
}
@media (max-width: 767px) {
  .about3 .images2 .circle-shape {
    left: 50px;
    bottom: 0;
  }
}
.about3 .images2 .circle-shape .arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -20px;
  margin-left: -18px;
}
.about3 .porgress-line-all {
  background-color: var(--vtc-bg-common-11);
  border-radius: 8px;
  padding: 32px;
  margin-top: 30px;
}
.about3 .porgress-line-all .progress-line {
  margin-top: 32px;
  position: relative;
}
.about3 .porgress-line-all .progress-line h6 {
  color: var(--vtc-text-title-4);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
}
.about3 .porgress-line-all .progress-line .percentCount {
  position: absolute;
  right: 0;
  top: 0;
  color: var(--vtc-text-title-4);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
}
.about3 .porgress-line-all .progress-line .progressbar {
  margin-top: 20px;
  color: red;
}
.about3 .porgress-line-all .progress-line .progressbar .proggress {
  background-color: #024912 !important;
}
.about3 .porgress-line-all .progress-line:nth-child(1) {
  margin-top: 0;
}
.about3 .about-counter-boxs .counter-box {
  text-align: center;
  margin-top: 30px;
  position: relative;
}
@media (max-width: 767px) {
  .about3 .about-counter-boxs .counter-box {
    text-align: start;
  }
}
.about3 .about-counter-boxs .counter-box h3 {
  color: var(--vtc-text-title-3);
  font-size: var(--f-fs-font-32);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-32); /* 100% */
}
.about3 .about-counter-boxs .counter-box.add-after::after {
  content: "";
  position: absolute;
  top: 20px;
  left: 0;
  height: 40px;
  width: 1px;
  background-color: var(--vtc-bg-common-8);
}
@media (max-width: 767px) {
  .about3 .about-counter-boxs .counter-box.add-after::after {
    display: none;
  }
}
.about3 .about-counter-boxs .counter-box.add-after2::after {
  content: "";
  position: absolute;
  top: 20px;
  left: -21px;
  height: 40px;
  width: 1px;
  background-color: var(--vtc-bg-common-8);
}
@media (max-width: 767px) {
  .about3 .about-counter-boxs .counter-box.add-after2::after {
    display: none;
  }
}

.dark-mode .about3 .images .shape img, .dark-mode .about3 .images2 .shape img {
  filter: brightness(0) invert(1);
}

.about4 .about-images .shape1 {
  position: absolute;
  top: -50px;
  left: -50px;
  z-index: -1;
}
.about4 .about-images .shape2 {
  position: absolute;
  bottom: 0px;
  left: -50px;
  z-index: -1;
}
.about4 .about-images .circle-shape-area {
  position: absolute;
  left: 230px;
  bottom: 30px;
}
.about4 .about-images .circle-shape-area .circle-shape-arrow {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px;
  margin-top: -19px;
}
.about4 .about4-icon-box {
  background-color: var(--vtc-bg-common-14);
  padding: 8px 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  margin-top: 30px;
  transition: all 0.4s;
}
.about4 .about4-icon-box .icon {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  background-color: var(--vtc-bg-white5);
  border-radius: 50%;
}
.about4 .about4-icon-box .text p {
  color: var(--vtc-text-title-6);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
  padding-left: 10px;
}
.about4 .about4-icon-box:hover {
  transition: all 0.4s;
  transform: translateY(-5px);
}

.about5 .heading5 .icon-text ul li {
  display: inline-flex;
  color: var(--vtc-text-title-7);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
  align-items: center;
  margin-top: 16px;
}
.about5 .heading5 .icon-text ul li .check {
  display: inline-block;
  height: 20px;
  width: 20px;
  text-align: center;
  line-height: 20px;
  border-radius: 50%;
  background: var(--vtc-bg-main10);
  font-size: 10px;
  color: var(--vtc-bg-white1);
  margin-right: 10px;
}

.about-page-brand {
  padding: 80px 0px;
}
.about-page-brand p {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 100% */
  text-transform: capitalize;
}
.about-page-brand .about-brand-slider {
  text-align: center;
  margin-top: 40px;
}
/*.about-page-brand */
.about-brand-slider .single-brand {
  background-color: var(--vtc-bg-white3);
  border-radius: 8px;
  width: 250px;
  text-align: center;
  padding: 20px;
  display: flex;
  justify-content: center;
  margin: 0px 10px;
}
.about-page-brand .about-brand-slider .single-brand img {
  transition: all 0.4s;
}

.dark-mode .about-page-brand .about-brand-slider .single-brand img {
  filter: brightness(0) invert(1);
}

.about-page-advicx .about-page-progress-area {
  background-color: var(--vtc-bg-common-2);
  padding: 20px;
  border-radius: 8px;
}
.about-page-advicx .about-page-progress-area .text-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.about-page-advicx .about-page-progress-area .text-area p {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
}
.about-page-advicx .about-page-progress-area .progress {
  --bs-progress-height: 1rem;
  --bs-progress-font-size: 0.75rem;
  --bs-progress-bg: #DBDEDE;
  --bs-progress-border-radius: 30px;
  --bs-progress-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --bs-progress-bar-color: #fff;
  --bs-progress-bar-bg: #10343B;
  --bs-progress-bar-transition: width 0.6s ease;
  display: flex;
  height: var(--bs-progress-height);
  overflow: hidden;
  font-size: var(--bs-progress-font-size);
  background-color: var(--bs-progress-bg);
  border-radius: var(--bs-progress-border-radius);
}
.about-page-advicx .images-all {
  position: relative;
  height: 536px;
  text-align: end;
}
.about-page-advicx .images-all .image2 {
  position: absolute;
  bottom: 0;
  left: 60px;
}
.about-page-advicx .images-all .shape {
  position: absolute;
  top: 0;
  left: 100px;
}

.dark-mode .about-page-advicx .images-all .shape {
  filter: brightness(40);
}

/*
 ::::::::::::::::::::::::::
  ABOUT AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 SERVICE AREA CSS
::::::::::::::::::::::::::
*/
.service1 .images {
  /*height: 380px;*/
  text-align: end;
}
.service1 .images .shape {
  position: absolute;
  right: -30px;
  top: -30px;
  z-index: -1;
}
.service1 .images .shape2 {
  position: absolute;
  bottom: 0;
  left: 0;
}

.service1-slider {
  position: relative;
}

.service1-single-slider {
  background-color: var(--vtc-bg-white3);
  padding: 32px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  transition: all 0.4s;
  margin: 0px 10px;
}
.service1-single-slider .icon {
  height: 70px;
  width: 70px;
  text-align: center;
  line-height: 70px;
  background-color: var(--vtc-bg-main3);
  border-radius: 50%;
  margin-bottom: 20px;
  transition: all 0.4s;
  display: inline-flex;
}
.service1-single-slider .icon img {
  transition: all 0.4s;
  filter: brightness(40);
  height: 41px;
  margin: auto;
}
.service1-single-slider .arrow {
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  color: var(--vtc-bg-main3);
  background-color: var(--vtc-bg-white2);
  border-radius: 50%;
  transform: rotate(-45deg);
  position: absolute;
  right: -50px;
  top: -50px;
  transition: all 0.4s;
}
.service1-single-slider .number {
  position: relative;
  padding-left: 76px;
}
.service1-single-slider .number::after {
  content: "";
  position: absolute;
  left: 0;
  top: 12px;
  height: 1px;
  width: 70px;
  background-color: var(--vtc-text-sub-title-1);
  transition: all 0.4s;
}
.service1-single-slider:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main3);
}
.service1-single-slider:hover h4 a {
  color: var(--vtc-bg-white3);
  transition: all 0.4s;
}
.service1-single-slider:hover .arrow {
  top: 16px;
  right: 16px;
}
.service1-single-slider:hover p {
  transition: all 0.4s;
  color: var(--vtc-bg-white3);
}
.service1-single-slider:hover .icon {
  background-color: var(--vtc-bg-white3);
  transition: all 0.4s;
}
.service1-single-slider:hover .icon img {
  filter: none;
  transition: all 0.4s;
}
.service1-single-slider:hover .number {
  transition: all 0.4s;
  padding-left: 0;
}
.service1-single-slider:hover .number::after {
  background-color: var(--vtc-bg-white3);
  transition: all 0.4s;
  left: 30px;
}

.dark-mode .service1-single-slider .icon img {
  filter: none;
}
.dark-mode .service1-single-slider:hover .icon img {
  filter: brightness(40);
}

.servie1-arrow-buttons {
  margin-top: 40px;
  margin-right: 10px;
}
.servie1-arrow-buttons button {
  display: inline-block;
  border: none;
  border-radius: 50%;
  height: 56px;
  width: 56px;
  text-align: center;
  line-height: 56px;
  background-color: var(--vtc-bg-main3);
  font-size: var(--f-fs-font-20);
  color: var(--vtc-bg-white2);
  transition: all 0.4s;
  margin-right: 5px;
}
.servie1-arrow-buttons button:hover {
  background-color: var(--vtc-bg-main1);
  color: var(--vtc-text-title-1);
}

.servie2-arrow-buttons {
  margin-top: 40px;
  margin-right: 10px;
}
.servie2-arrow-buttons button {
  display: inline-block;
  border: none;
  border-radius: 50%;
  height: 56px;
  width: 56px;
  text-align: center;
  line-height: 56px;
  background-color: var(--vtc-bg-main4);
  font-size: var(--f-fs-font-20);
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
  margin-right: 5px;
}
.servie2-arrow-buttons button:hover {
  background-color: var(--vtc-bg-main5);
  color: var(--vtc-bg-white1);
}

.service2-single-slider {
  background-color: var(--vtc-bg-white5);
  border-radius: 8px;
  padding: 20px 20px 32px 20px;
  text-align: center;
  margin: 0px 10px;
}
.service2-single-slider .arrow {
  display: inline-block;
  height: 48px;
  width: 48px;
  border-radius: 50%;
  text-align: center;
  line-height: 48px;
  background-color: var(--vtc-bg-main4);
  position: absolute;
  top: 50%;
  left: 50%;
  color: var(--vtc-bg-white1);
  margin-top: -24px;
  margin-left: -24px;
  transform: rotate(-45deg) translateX(-133px) translateY(-25px) scale(0.2);
  z-index: 7;
  transition: all 0.4s;
  opacity: 0;
}
.service2-single-slider .arrow:hover {
  background-color: var(--vtc-bg-main5);
}
.service2-single-slider .icon {
  height: 90px;
  width: 90px;
  text-align: center;
  line-height: 90px;
  background-color: var(--vtc-bg-common-8);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -45px;
  position: relative;
  left: 50%;
  z-index: 6;
  margin-left: -45px;
  border: 5px solid var(--vtc-bg-white1);
}
.service2-single-slider .icon img {
  width: 40px;
  transition: all 0.4s;
}
.service2-single-slider .image {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}
.service2-single-slider .image img {
  width: 100%;
  transition: all 0.4s;
}
.service2-single-slider .image::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.4039215686);
  transition: all 0.4s;
  transform: scale(0.6);
  opacity: 0;
}
.service2-single-slider:hover {
  transition: all 0.4s;
}
.service2-single-slider:hover .arrow {
  transform: rotate(-45deg) translateX(0) translateY(0);
  transition: all 0.4s;
  opacity: 1;
}
.service2-single-slider:hover .icon img {
  transition: all 0.4s;
  transform: rotateY(180deg);
}
.service2-single-slider:hover .image::after {
  transform: scale(1);
  opacity: 1;
}
.service2-single-slider:hover .image img {
  transform: scale(1.1) rotate(4deg);
  transition: all 0.4s;
  filter: grayscale(1);
}

.service3-box {
  text-align: center;
  border-radius: 16px;
  background-color: var(--vtc-bg-white1);
  padding: 42px 32px 0px 32px;
  transition: all 0.4s;
}
.service3-box .image {
  border-radius: 16px;
  overflow: hidden;
  height: 345px;
}
.service3-box .image img {
  transition: all 0.4s;
}
.service3-box .icon {
  height: 72px;
  width: 72px;
  text-align: center;
  line-height: 72px;
  background-color: var(--vtc-bg-main6);
  border-radius: 50%;
  margin: auto;
  position: relative;
}
.service3-box .icon .icon-shape {
  position: absolute;
  top: 0;
  left: 0;
  filter: none;
  transform: scale(1.3);
}
.service3-box .icon img {
  transition: all 0.4s;
  filter: brightness(0) invert(1);
}
.service3-box .learn2 {
  transform: translateY(24px);
}
.service3-box:hover {
  transform: translateY(-10px);
}
.service3-box:hover .icon .icon-shape {
  animation-name: service-circle;
  animation-duration: 32s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: linear;
}
.service3-box:hover .image img {
  transition: all 0.4s;
}
.service3-box:hover .learn2 span {
  transition: all 0.4s;
  background-color: #a7d35a;
}
.service3-box:hover .learn2 .arrow-all {
  transform: translateX(3px);
  transition: all 0.4s;
}
.service3-box:hover .learn2 .arrow-all .arrow2 {
  transform: translateY(-7px) rotate(-45deg) translateX(-10px);
  transition: all 0.4s;
  opacity: 1;
}
.service3-box:hover .learn2 .arrow-all .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

@keyframes service-circle {
  0% {
    transform: rotate(0deg) scale(1.3);
  }
  100% {
    transform: rotate(1000deg) scale(1.3);
  }
}
.service4 {
  position: relative;
}
.service4 .shape {
  position: absolute;
  top: 0;
  z-index: 1;
  right: 0;
}

.service4-box {
  background-color: var(--vtc-bg-white6);
  padding: 32px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  transition: all 0.4s;
}
.service4-box .icon {
  height: 70px;
  width: 70px;
  text-align: center;
  line-height: 70px;
  background-color: var(--vtc-bg-main8);
  border-radius: 50%;
  margin-bottom: 20px;
  transition: all 0.4s;
  display: inline-flex;
}
.service4-box .icon img {
  transition: all 0.4s;
  filter: brightness(40);
  height: 41px;
  margin: auto;
}
.service4-box .arrow {
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  color: var(--vtc-bg-main8);
  background-color: var(--vtc-bg-white1);
  border-radius: 50%;
  transform: rotate(-45deg);
  position: absolute;
  right: -50px;
  top: -50px;
  transition: all 0.4s;
}
.service4-box .number {
  position: relative;
  padding-left: 76px;
  transition: all 0.4s;
  color: var(--vtc-bg-main8);
}
.service4-box .number::after {
  content: "";
  position: absolute;
  left: 0;
  top: 12px;
  height: 1px;
  width: 70px;
  background-color: var(--vtc-bg-main8);
  transition: all 0.4s;
}
.service4-box:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main8);
}
.service4-box:hover h4 a {
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
}
.service4-box:hover .arrow {
  top: 16px;
  right: 16px;
}
.service4-box:hover p {
  transition: all 0.4s;
  color: var(--vtc-bg-white1);
}
.service4-box:hover .icon {
  background-color: var(--vtc-bg-white1);
  transition: all 0.4s;
}
.service4-box:hover .icon img {
  filter: none;
  transition: all 0.4s;
}
.service4-box:hover .number {
  transition: all 0.4s;
  padding-left: 0;
}
.service4-box:hover .number::after {
  background-color: var(--vtc-bg-white1);
  transition: all 0.4s;
  left: 30px;
}

.service5 {
  position: relative;
  z-index: 1;
}
.service5 .service-widgets-section {
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.service5 .service-widgets-section .tab-content .tab-pane {
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: var(--vtc-bg-common-17);
  -webkit-backdrop-filter: blur(15px);
          backdrop-filter: blur(15px);
  padding: 40px 50px;
  transform: rotateX(45deg) translateY(50px);
  transition: all 0.4s;
  opacity: 0;
  overflow: hidden;
}
@media (max-width: 767px) {
  .service5 .service-widgets-section .tab-content .tab-pane {
    padding: 24px;
  }
}
.service5 .service-widgets-section .tab-content .tab-pane.fade.show.active {
  transform: rotateX(0deg) translateY(0);
  opacity: 1;
}
.service5 .service-widgets-section .tab-content .tab-pane .service-boxarea .icons {
  background: var(--vtc-bg-main10);
  border-width: 1.5px;
  display: inline-block;
  transition: all 0.4s;
  border-radius: 50%;
  height: 90px;
  width: 90px;
  text-align: center;
  line-height: 90px;
}
.service5 .service-widgets-section .tab-content .tab-pane .service-boxarea .icons img {
  height: 50px;
  width: 50px;
  -o-object-fit: contain;
     object-fit: contain;
}
.service5 .service-widgets-section .tab-content .tab-pane .service-boxarea .content-area h3 {
  color: var(--vtc-text-title-7);
  font-size: 28px;
  font-style: normal;
  font-weight: 600;
  line-height: 28px; /* 100% */
  display: inline-block;
}
.service5 .service-widgets-section .tab-content .tab-pane .service-boxarea .content-area p {
  color: var(--vtc-text-pera-7);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-26); /* 144.444% */
  padding-top: 20px;
}
.service5 .service-widgets-section .tab-content .tab-pane .images-area {
  position: relative;
}
@media (max-width: 767px) {
  .service5 .service-widgets-section .tab-content .tab-pane .images-area {
    margin-top: 30px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .service5 .service-widgets-section .tab-content .tab-pane .images-area {
    margin-top: 30px;
  }
}
.service5 .service-widgets-section .tab-content .tab-pane .images-area .img1 img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.service5 .service-widgets-section .tab-content .tab-pane .images-area .arrow-circle a {
  height: 110px;
  width: 101px;
  display: inline-block;
  transition: all 0.4s;
  border-radius: 50%;
  background: var(--ztc-bg-bg-6);
  position: absolute;
  bottom: 30px;
  left: -60px;
  z-index: 1;
}
.service5 .service-widgets-section .tab-content .tab-pane .images-area .arrow-circle a .arrow1 {
  position: absolute;
  top: 41%;
  left: 44%;
}
.service5 .service-widgets-section .tab-content .tab-pane .images-area .arrow-circle a .elements20 {
  position: absolute;
  top: 6px;
  left: 6px;
}
.service5 .service-widgets-section .tabs-btn-area {
  position: relative;
  z-index: 1;
}
.service5 .service-widgets-section .tabs-btn-area::after {
  position: absolute;
  content: "";
  height: 10px;
  width: 100%;
  background: var(--vtc-bg-common-17);
  top: -32px;
  left: 0;
  right: 0;
  transition: all 0.4s;
  border-radius: 30px;
}
.service5 .service-widgets-section .tabs-btn-area ul {
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 767px) {
  .service5 .service-widgets-section .tabs-btn-area ul {
    justify-content: center;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .service5 .service-widgets-section .tabs-btn-area ul li:nth-child(4) {
    margin-top: 20px;
  }
}
.service5 .service-widgets-section .tabs-btn-area ul li button {
  border-radius: 120px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: var(--vtc-bg-common-17);
  padding: 8px 16px 8px 8px;
  position: relative;
  z-index: 1;
  color: var(--vtc-text-title-7);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
}
@media (max-width: 767px) {
  .service5 .service-widgets-section .tabs-btn-area ul li button {
    display: block !important;
    margin-bottom: 16px;
  }
}
.service5 .service-widgets-section .tabs-btn-area ul li button::after {
  position: absolute;
  z-index: 1;
  content: "";
  height: 10px;
  left: 0;
  top: -32px;
  transition: all 0.4s;
  width: 100%;
  background: var(--vtc-bg-main10);
  border-radius: 40px;
  visibility: hidden;
  opacity: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .service5 .service-widgets-section .tabs-btn-area ul li button::after {
    display: none;
  }
}
@media (max-width: 767px) {
  .service5 .service-widgets-section .tabs-btn-area ul li button::after {
    display: none;
  }
}
.service5 .service-widgets-section .tabs-btn-area ul li button.active {
  background: var(--vtc-bg-main10);
  color: var(--vtc-bg-white1);
}
.service5 .service-widgets-section .tabs-btn-area ul li button.active::after {
  visibility: visible;
  opacity: 1;
  transition: all 0.4s;
}
.service5 .service-widgets-section .tabs-btn-area ul li button.active span {
  background: var(--vtc-bg-main12);
}
.service5 .service-widgets-section .tabs-btn-area ul li button.active span img {
  transition: all 0.4s;
  filter: brightness(40);
}
.service5 .service-widgets-section .tabs-btn-area ul li button span {
  height: 48px;
  width: 48px;
  text-align: center;
  line-height: 48px;
  border-radius: 50%;
  transition: all 0.4s;
  display: inline-block;
  background: var(--vtc-bg-main10);
  border-width: 1px;
  border: rgba(192, 240, 55, 0.1);
  margin: 0 8px 0 0;
}
.service5 .service-widgets-section .tabs-btn-area ul li button span img {
  height: 28px;
  width: 28px;
  -o-object-fit: contain;
     object-fit: contain;
  transition: all 0.4s;
}

.service-page-sec1 .images {
  height: 380px;
  text-align: start;
}
.service-page-sec1 .images .shape {
  position: absolute;
  left: -30px;
  top: -30px;
  z-index: -1;
}
.service-page-sec1 .images .shape2 {
  position: absolute;
  bottom: 0;
  right: 0;
}

.service-page-item {
  background-color: var(--vtc-bg-white2);
  padding: 32px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  transition: all 0.4s;
  box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.09);
}
.service-page-item .icon {
  height: 70px;
  width: 70px;
  text-align: center;
  line-height: 70px;
  background-color: var(--vtc-bg-main3);
  border-radius: 50%;
  margin-bottom: 20px;
  transition: all 0.4s;
  display: inline-flex;
}
.service-page-item .icon img {
  transition: all 0.4s;
  filter: brightness(40);
  height: 41px;
  margin: auto;
}
.service-page-item .arrow {
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  color: var(--vtc-bg-main3);
  background-color: var(--vtc-bg-white2);
  border-radius: 50%;
  transform: rotate(-45deg);
  position: absolute;
  right: -50px;
  top: -50px;
  transition: all 0.4s;
}
.service-page-item .number {
  position: relative;
  padding-left: 76px;
}
.service-page-item .number::after {
  content: "";
  position: absolute;
  left: 0;
  top: 12px;
  height: 1px;
  width: 70px;
  background-color: var(--vtc-text-sub-title-1);
  transition: all 0.4s;
}
.service-page-item:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main3);
}
.service-page-item:hover h4 a {
  color: var(--vtc-bg-white3);
  transition: all 0.4s;
}
.service-page-item:hover .arrow {
  top: 16px;
  right: 16px;
}
.service-page-item:hover p {
  transition: all 0.4s;
  color: var(--vtc-bg-white3);
}
.service-page-item:hover .icon {
  background-color: var(--vtc-bg-white3);
  transition: all 0.4s;
}
.service-page-item:hover .icon img {
  filter: none;
  transition: all 0.4s;
}
.service-page-item:hover .number {
  transition: all 0.4s;
  padding-left: 0;
}
.service-page-item:hover .number::after {
  background-color: var(--vtc-bg-white3);
  transition: all 0.4s;
  left: 30px;
}

.dark-mode .service-page-item .icon img {
  filter: none;
}
.dark-mode .service-page-item:hover .icon img {
  filter: brightness(40);
}

/*
 ::::::::::::::::::::::::::
  SERVICE AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 FAQ AREA CSS
::::::::::::::::::::::::::
*/
.details-content {
  position: relative;
}
.details-content .accordion .accordion-item {
  border: none;
  border-radius: 14px;
  background: none;
  margin-top: 20px;
  transition: all 0.4s;
  background-color: var(--vtc-bg-common-2);
}
.details-content .accordion .accordion-item button {
  border: none;
  background: none;
  padding: 24px 58px 24px 24px;
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: 600;
  line-height: var(--f-fs-font-24); /* 100% */
  text-transform: capitalize;
  position: relative;
}
@media (max-width: 767px) {
  .details-content .accordion .accordion-item button {
    font-size: 20px;
    line-height: 26px;
    padding: 20px;
  }
}
.details-content .accordion .accordion-item button:focus {
  outline: none;
  box-shadow: none;
}
.details-content .accordion .accordion-item button::before {
  content: "";
  position: absolute;
  right: 16px;
  top: 22px;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  z-index: 1;
  background: var(--vtc-bg-main3);
}
@media (max-width: 767px) {
  .details-content .accordion .accordion-item button::before {
    display: none;
  }
}
.details-content .accordion .accordion-item button::after {
  filter: brightness(0) invert(1);
  z-index: 3;
  height: 14px;
  width: 22px;
  position: absolute;
  right: 18px;
  top: 26px;
}
.details-content .accordion .accordion-item .accordion-body {
  color: rgba(255, 255, 255, 0.9098039216);
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 28px; /* 155.556% */
  padding-left: 28px;
  margin-top: -20px;
  padding-bottom: 28px;
  padding-right: 80px;
  text-align: start;
}
@media (max-width: 767px) {
  .details-content .accordion .accordion-item .accordion-body {
    padding-right: 20px;
    padding-bottom: 20px;
    padding-left: 20px;
  }
}
.details-content .accordion .accordion-item.active {
  transition: all 0.4s;
  border-radius: 14px;
  background: var(--vtc-bg-main2);
}
.details-content .accordion .accordion-item.active button {
  color: #fff;
}
.details-content .accordion .accordion-item.active button::before {
  content: "";
  position: absolute;
  right: 16px;
  top: 22px;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  z-index: 1;
  background: #fff;
}
.details-content .accordion .accordion-item.active button::after {
  content: "";
  position: absolute;
  right: 18px;
  top: 26px;
  filter: brightness(0);
  height: 14px;
  width: 22px;
}
.details-content .accordion-button:not(.collapsed) {
  color: var(--bs-accordion-active-color);
  background-color: var(--bs-accordion-active-bg);
  box-shadow: none;
}
.details-content .sec-shape1 {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: -3;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .details-content .sec-shape1 {
    display: none;
  }
}
@media (max-width: 767px) {
  .details-content .sec-shape1 {
    display: none;
  }
}
.details-content .sec-shape2 {
  position: absolute;
  top: -100px;
  right: 0;
  z-index: -3;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .details-content .sec-shape2 {
    display: none;
  }
}
@media (max-width: 767px) {
  .details-content .sec-shape2 {
    display: none;
  }
}
.details-content .sec-shape3 {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -3;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .details-content .sec-shape3 {
    display: none;
  }
}
@media (max-width: 767px) {
  .details-content .sec-shape3 {
    display: none;
  }
}
.details-content .sec-shape4 {
  position: absolute;
  top: 0;
  right: 0;
  z-index: -3;
}
.details-content .sec-shape5 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -3;
}
.details-content .sec-shape6 {
  position: absolute;
  bottom: 200px;
  left: 0;
  z-index: -3;
  transform: rotateX(180deg) rotateY(180deg);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .details-content .sec-shape6 {
    display: none;
  }
}
@media (max-width: 767px) {
  .details-content .sec-shape6 {
    display: none;
  }
}

.dark-mode .details-content .accordion .accordion-item button::after {
  filter: none;
}

.faq-content {
  position: relative;
}
.faq-content .accordion .accordion-item {
  border: none;
  border-radius: 14px;
  background: none;
  margin-top: 20px;
  transition: all 0.4s;
  background-color: var(--vtc-bg-common-2);
}
.faq-content .accordion .accordion-item button {
  border: none;
  background: none;
  padding: 24px;
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: 600;
  line-height: var(--f-fs-font-18); /* 100% */
  text-transform: capitalize;
  position: relative;
}
@media (max-width: 767px) {
  .faq-content .accordion .accordion-item button {
    font-size: 20px;
    line-height: 26px;
    padding: 20px;
  }
}
.faq-content .accordion .accordion-item button:focus {
  outline: none;
  box-shadow: none;
}
.faq-content .accordion .accordion-item button::before {
  content: "";
  position: absolute;
  right: 16px;
  top: 22px;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  z-index: 1;
  background: var(--vtc-bg-main3);
}
@media (max-width: 767px) {
  .faq-content .accordion .accordion-item button::before {
    display: none;
  }
}
.faq-content .accordion .accordion-item button::after {
  filter: brightness(0) invert(1);
  z-index: 3;
  height: 14px;
  width: 22px;
  position: absolute;
  right: 18px;
  top: 26px;
}
.faq-content .accordion .accordion-item .accordion-body {
  color: rgba(255, 255, 255, 0.9098039216);
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 28px; /* 155.556% */
  padding-left: 28px;
  margin-top: -20px;
  padding-bottom: 28px;
  padding-right: 80px;
  text-align: start;
}
@media (max-width: 767px) {
  .faq-content .accordion .accordion-item .accordion-body {
    padding-right: 20px;
    padding-bottom: 20px;
    padding-left: 20px;
  }
}
.faq-content .accordion .accordion-item.active {
  transition: all 0.4s;
  border-radius: 14px;
  background: var(--vtc-bg-main2);
}
.faq-content .accordion .accordion-item.active button {
  color: #fff;
}
.faq-content .accordion .accordion-item.active button::before {
  content: "";
  position: absolute;
  right: 16px;
  top: 22px;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  z-index: 1;
  background: #fff;
}
.faq-content .accordion .accordion-item.active button::after {
  content: "";
  position: absolute;
  right: 18px;
  top: 26px;
  filter: brightness(0);
  height: 14px;
  width: 22px;
}
.details-content .accordion .accordion-item.active button:not(.collapsed)::after,
.faq-content .accordion .accordion-item.active button:not(.collapsed)::after {
  right: 20px;
  top: 30px;
}
.faq-content .accordion-button:not(.collapsed) {
  color: var(--bs-accordion-active-color);
  background-color: var(--bs-accordion-active-bg);
  box-shadow: none;
}
.faq-content .sec-shape1 {
  position: absolute;
  top: -100px;
  left: 0;
  z-index: -3;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .faq-content .sec-shape1 {
    display: none;
  }
}
@media (max-width: 767px) {
  .faq-content .sec-shape1 {
    display: none;
  }
}
.faq-content .sec-shape2 {
  position: absolute;
  top: -100px;
  right: 0;
  z-index: -3;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .faq-content .sec-shape2 {
    display: none;
  }
}
@media (max-width: 767px) {
  .faq-content .sec-shape2 {
    display: none;
  }
}
.faq-content .sec-shape3 {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -3;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .faq-content .sec-shape3 {
    display: none;
  }
}
@media (max-width: 767px) {
  .faq-content .sec-shape3 {
    display: none;
  }
}
.faq-content .sec-shape4 {
  position: absolute;
  top: 0;
  right: 0;
  z-index: -3;
}
.faq-content .sec-shape5 {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -3;
}
.faq-content .sec-shape6 {
  position: absolute;
  bottom: 200px;
  left: 0;
  z-index: -3;
  transform: rotateX(180deg) rotateY(180deg);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .faq-content .sec-shape6 {
    display: none;
  }
}
@media (max-width: 767px) {
  .faq-content .sec-shape6 {
    display: none;
  }
}

.dark-mode .faq-content .accordion .accordion-item button::after {
  filter: none;
}

/*
 ::::::::::::::::::::::::::
  FAQ AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 CASE STUDY AREA CSS
::::::::::::::::::::::::::
*/
.case1-box {
  position: relative;
  height: 270px;
  overflow: hidden;
  border-radius: 8px;
}
.case1-box .image img {
  width: 100%;
  transition: all 0.4s;
  -o-object-fit: cover;
     object-fit: cover;
  height: 300px;
}
.case1-box .content-area {
  background-color: var(--vtc-bg-white1);
  padding: 32px;
  border-radius: 8px;
  text-align: center;
  margin: 0px 40px;
  position: absolute;
  bottom: 35px;
  width: 80%;
  transform: scale(0.3) translateY(130px);
  opacity: 0;
  transition: all 0.4s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .case1-box .content-area {
    transform: scale(1) translateY(0);
    opacity: 1;
    bottom: 20px;
    margin: 0px 20px;
  }
}
@media (max-width: 767px) {
  .case1-box .content-area {
    transform: scale(1) translateY(0);
    opacity: 1;
    bottom: 20px;
    margin: 0px 20px;
    width: 88%;
  }
}
.case1-box .content-area .arrow {
  display: inline-block;
  background-color: var(--vtc-bg-main1);
  height: 54px;
  width: 54px;
  line-height: 54px;
  text-align: center;
  font-size: var(--f-fs-font-20);
  color: var(--vtc-bg-main2);
  border-radius: 50%;
  transform: rotate(-45deg);
}
.case1-box .content-area.content2 {
  width: 88%;
}
.case1-box:hover {
  transition: all 0.4s;
}
.case1-box:hover .content-area {
  transition: all 0.4s;
  transform: translateY(0) scale(1);
  opacity: 1;
}
.case1-box:hover .image img {
  filter: grayscale(1);
  transition: all 0.4s;
  transform: scale(1.1) rotate(2deg);
}

.case2-arrow-buttons {
  margin-top: 40px;
  margin-right: 10px;
}
.case2-arrow-buttons button {
  display: inline-block;
  border: none;
  border-radius: 50%;
  height: 56px;
  width: 56px;
  text-align: center;
  line-height: 56px;
  background-color: var(--vtc-bg-main4);
  font-size: var(--f-fs-font-20);
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
  margin-right: 5px;
}
.case2-arrow-buttons button:hover {
  background-color: var(--vtc-bg-main5);
  color: var(--vtc-bg-white1);
}

.case2-single-slider {
  margin: 0px 10px;
}
.case2-single-slider .image-area {
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}
.case2-single-slider .image-area .image {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}
.case2-single-slider .image-area .image img {
  width: 100%;
}
.case2-single-slider .image-area .image::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: var(--vtc-text-title-2);
  opacity: 0;
  transition: all 0.4s;
  transform: scale(0.6);
}
.case2-single-slider .image-area .hover-area {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 220px;
  z-index: 3;
  margin-top: -60px;
  margin-left: -110px;
  transform: scale(0.5) translateY(80px);
  opacity: 0;
  transition: all 0.4s;
}
.case2-single-slider .image-area .hover-area p {
  color: var(--vtc-bg-main4);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
  padding: 16px 0px 12px 0px;
}
.case2-single-slider .image-area .hover-area h4 a {
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 100% */
  transition: all 0.4s;
}
.case2-single-slider .image-area .hover-area h4 a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main4);
}
.case2-single-slider .image-area .hover-area .arrow {
  display: inline-block;
  width: 54px;
  height: 54px;
  text-align: center;
  line-height: 54px;
  background-color: var(--vtc-bg-main4);
  border-radius: 50%;
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  transform: rotate(-45deg);
  transition: all 0.4s;
}
.case2-single-slider .image-area .hover-area .arrow:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main5);
}
.case2-single-slider:hover .image-area .image::after {
  transform: scale(1);
  opacity: 0.6;
  transition: all 0.4s;
}
.case2-single-slider:hover .image-area .hover-area {
  transform: scale(1) translateY(0);
  opacity: 1;
  transition: all 0.4s;
}
.case2-single-slider.slick-current.slick-active .image-area .image::after {
  transform: scale(1);
  opacity: 0.6;
  transition: all 0.4s;
}
.case2-single-slider.slick-current.slick-active .image-area .hover-area {
  transform: scale(1) translateY(0);
  opacity: 1;
  transition: all 0.4s;
}

.case3-single-slider {
  background-color: var(--vtc-bg-white7);
  border-radius: 16px;
  margin: 0px 10px;
}
.case3-single-slider .image {
  overflow: hidden;
  border-radius: 16px 16px 0px 0px;
}
.case3-single-slider .image img {
  transition: all 0.4s;
  width: 100%;
}
.case3-single-slider .content-area {
  padding: 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.case3-single-slider .content-area .arrow {
  display: inline-block;
  height: 54px;
  width: 54px;
  text-align: center;
  line-height: 54px;
  background-color: var();
}
.case3-single-slider .content-area .heading p {
  color: var(--vtc-text-sub-title-2);
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 18px; /* 100% */
}
.case3-single-slider .content-area .heading h4 a {
  color: var(--vtc-text-title-3);
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 100% */
  display: inline-block;
  padding-top: 12px;
}
.case3-single-slider .content-area .heading h4 a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main6);
}
.case3-single-slider:hover .image img {
  transform: scale(1.1) rotate(2deg);
}
.case3-single-slider:hover .learn2 span {
  transition: all 0.4s;
  background-color: #a7d35a;
}
.case3-single-slider:hover .learn2 .arrow-all {
  transform: translateX(3px);
  transition: all 0.4s;
}
.case3-single-slider:hover .learn2 .arrow-all .arrow2 {
  transform: translateY(-7px) rotate(-45deg) translateX(-10px);
  transition: all 0.4s;
  opacity: 1;
}
.case3-single-slider:hover .learn2 .arrow-all .arrow1 {
  transition: all 0.4s;
  transform: translateY(-7px) rotate(-45deg) translateX(45px);
  opacity: 0;
}

.case3-arrow-buttons {
  margin-top: 40px;
  margin-right: 10px;
}
.case3-arrow-buttons button {
  display: inline-block;
  border: none;
  border-radius: 50%;
  height: 56px;
  width: 56px;
  text-align: center;
  line-height: 56px;
  background-color: rgba(133, 176, 60, 0.231372549);
  font-size: var(--f-fs-font-20);
  color: var(--vtc-text-title-3);
  transition: all 0.4s;
  margin-right: 5px;
}
.case3-arrow-buttons button:hover {
  background-color: var(--vtc-bg-main6);
  color: var(--vtc-bg-white1);
}

.case4-arrow-buttons {
  margin-top: 40px;
  margin-right: 10px;
}
.case4-arrow-buttons button {
  display: inline-block;
  border: none;
  border-radius: 50%;
  height: 56px;
  width: 56px;
  text-align: center;
  line-height: 56px;
  background-color: var(--vtc-bg-main8);
  font-size: var(--f-fs-font-20);
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
  margin-right: 5px;
}
.case4-arrow-buttons button:hover {
  background-color: var(--vtc-bg-main9);
  color: var(--vtc-bg-white7);
}

.case4-single-slider {
  margin: 0px 10px;
}
.case4-single-slider .image-area {
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}
.case4-single-slider .image-area .image {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}
.case4-single-slider .image-area .image img {
  width: 100%;
}
.case4-single-slider .image-area .image::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: var(--vtc-text-title-2);
  opacity: 0;
  transition: all 0.4s;
  transform: scale(0.6);
}
.case4-single-slider .image-area .hover-area {
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 220px;
  z-index: 3;
  margin-top: -60px;
  margin-left: -110px;
  transform: scale(0.5) translateY(80px);
  opacity: 0;
  transition: all 0.4s;
}
.case4-single-slider .image-area .hover-area p {
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
  padding: 16px 0px 12px 0px;
}
.case4-single-slider .image-area .hover-area h4 a {
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 100% */
  transition: all 0.4s;
}
.case4-single-slider .image-area .hover-area h4 a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main8);
}
.case4-single-slider .image-area .hover-area .arrow {
  display: inline-block;
  width: 54px;
  height: 54px;
  text-align: center;
  line-height: 54px;
  background-color: var(--vtc-bg-main8);
  border-radius: 50%;
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  transform: rotate(-45deg);
  transition: all 0.4s;
}
.case4-single-slider .image-area .hover-area .arrow:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main9);
}
.case4-single-slider:hover .image-area .image::after {
  transform: scale(1);
  opacity: 0.6;
  transition: all 0.4s;
}
.case4-single-slider:hover .image-area .hover-area {
  transform: scale(1) translateY(0);
  opacity: 1;
  transition: all 0.4s;
}
.case4-single-slider.slick-current.slick-active .image-area .image::after {
  transform: scale(1);
  opacity: 0.6;
  transition: all 0.4s;
}
.case4-single-slider.slick-current.slick-active .image-area .hover-area {
  transform: scale(1) translateY(0);
  opacity: 1;
  transition: all 0.4s;
}

.case4 .shape1 {
  position: absolute;
  left: 0;
  top: 0;
}
.case4 .shape2 {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
}

/*
 ::::::::::::::::::::::::::
  CASE STUDY AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 CHOOSE AREA CSS
::::::::::::::::::::::::::
*/
.choose2 .choose-text-box {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60px;
  padding-left: 40px;
  position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .choose2 .choose-text-box {
    margin-top: 40px;
  }
}
@media (max-width: 767px) {
  .choose2 .choose-text-box {
    margin-top: 40px;
  }
}
.choose2 .choose-text-box h3 {
  color: var(--vtc-text-title-3);
  font-size: var(--f-fs-font-56);
  font-style: italic;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-56); /* 100% */
}
.choose2 .choose-text-box p {
  color: var(--vtc-text-pera-4);
  font-size: var(--f-fs-font-20);
  font-style: italic;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-26); /* 130% */
  padding-left: 20px;
}
.choose2 .choose-text-box.add-after::after {
  content: "";
  position: absolute;
  right: 0;
  top: 10px;
  height: 40px;
  width: 1px;
  background-color: var(--vtc-text-pera-4);
  opacity: 0.5;
}
.choose2 .choose-text-box.add-after2::after {
  content: "";
  position: absolute;
  right: -30px;
  top: 10px;
  height: 40px;
  width: 1px;
  background-color: var(--vtc-text-pera-4);
  opacity: 0.5;
}
.choose2 .bottom-icon-box {
  background-color: var(--vtc-bg-white1);
  padding: 16px 20px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  margin: -60px 30px 0px 30px;
  position: relative;
  z-index: 4;
}
.choose2 .bottom-icon-box .icon {
  background-color: var(--vtc-bg-main4);
  height: 80px;
  width: 80px;
  line-height: 80px;
  text-align: center;
  border-radius: 50%;
  transition: all 0.4s;
}
.choose2 .bottom-icon-box .heading2 {
  padding-left: 16px;
}
.choose2 .bottom-icon-box .heading2 h3 {
  color: var(--vtc-text-title-4);
  font-size: var(--f-fs-font-22);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-22); /* 100% */
}
.choose2 .bottom-icon-box .heading2 p {
  color: var(--Home-Page-2-Color-Paragraph-Color, #4E4D59);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-16); /* 100% */
  padding-top: 12px;
}

.choose2-items-area .check-items {
  display: inline-flex;
  background-color: var(--vtc-bg-white6);
  padding: 12px;
  border-radius: 111px;
  align-items: center;
  min-width: 280px;
  transition: all 0.4s;
}
.choose2-items-area .check-items p {
  color: var(--vtc-text-title-3);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-20); /* 100% */
  padding-left: 16px;
  transition: all 0.4s;
}
.choose2-items-area .check-items.left-side {
  position: relative;
}
.choose2-items-area .check-items.left-side::after {
  content: "";
  position: absolute;
  top: 34px;
  right: -200px;
  height: 1px;
  width: 280px;
  background-color: var(--vtc-text-pera-4);
  opacity: 0.3;
  z-index: -1;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .choose2-items-area .check-items.left-side::after {
    display: none;
  }
}
@media (max-width: 767px) {
  .choose2-items-area .check-items.left-side::after {
    display: none;
  }
}
.choose2-items-area .check-items.right-side {
  position: relative;
}
.choose2-items-area .check-items.right-side::after {
  content: "";
  position: absolute;
  top: 34px;
  left: -200px;
  height: 1px;
  width: 280px;
  background-color: var(--vtc-text-pera-4);
  opacity: 0.3;
  z-index: -1;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .choose2-items-area .check-items.right-side::after {
    display: none;
  }
}
@media (max-width: 767px) {
  .choose2-items-area .check-items.right-side::after {
    display: none;
  }
}
.choose2-items-area .check-items:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main5);
}
.choose2-items-area .check-items:hover .check {
  background-color: var(--vtc-bg-main4);
  transition: all 0.4s;
}
.choose2-items-area .check-items:hover p {
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
}
.choose2-items-area .check {
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  background-color: var(--vtc-bg-main5);
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  transition: all 0.4s;
}

.choose5 .shape1 {
  right: -57px;
  width: 200px;
  position: absolute;
  top: -50px;
  z-index: -1;
}
.choose5 .shape2 {
  right: -57px;
  width: 200px;
  position: absolute;
  bottom: -50px;
  z-index: -1;
}

.choose-boxs {
  margin-right: -120px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .choose-boxs {
    margin-right: 0;
  }
}
@media (max-width: 767px) {
  .choose-boxs {
    margin-right: 0;
  }
}

.choose5-box {
  border-radius: 8px;
  background-color: var(--vtc-bg-white8);
  padding: 24px 26px;
  text-align: center;
  transition: all 0.4s;
}
.choose5-box .icon {
  height: 60px;
  width: 60px;
  text-align: center;
  line-height: 60px;
  border-radius: 50%;
  background: var(--vtc-bg-main10);
  margin: auto;
}
.choose5-box .icon img {
  transition: all 0.4s;
}
.choose5-box .content {
  padding-top: 16px;
}
.choose5-box .content h4 a {
  display: inline-block;
  color: var(--vtc-text-title-7);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: 20px; /* 100% */
  transition: all 0.4s;
}
.choose5-box .content h4 a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main12);
}
.choose5-box .content p {
  color: var(--vtc-text-pera-7);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-24); /* 150% */
  padding-top: 12px;
}
.choose5-box:hover {
  transition: all 0.4s;
  transform: translateY(-8px);
}
.choose5-box:hover .icon img {
  transition: all 0.4s;
  transform: rotateY(180deg);
}

/*
 ::::::::::::::::::::::::::
  CHOOSE AREA CSS
 ::::::::::::::::::::::::::
 */
/*
 ::::::::::::::::::::::::::
  TEAM AREA CSS
 ::::::::::::::::::::::::::
 */
.team1-box {
  text-align: center;
  transition: all 0.4s;
}
.team1-box .image-area {
  background-color: var(--vtc-bg-common-2);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  min-height: 260px;
}
.team1-box .image-area .image {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.team1-box .image-area .image img {
  transition: all 0.4s;
}
.team1-box .image-area .icons {
  position: absolute;
  bottom: 30px;
  left: 50%;
  margin-left: -95px;
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.4s;
}
.team1-box .image-area .icons ul li {
  display: inline-block;
}
.team1-box .image-area .icons ul li a {
  display: inline-block;
  color: var(--vtc-text-title-2);
  height: 40px;
  text-align: center;
  line-height: 40px;
  width: 40px;
  border-radius: 50%;
  background-color: var(--vtc-bg-white1);
  margin: 0px 2px;
  transition: all 0.4s;
}
.team1-box .image-area .icons ul li a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main1);
}
.team1-box:hover {
  transform: translateY(-10px);
  transition: all 0.4s;
}
.team1-box:hover .icons {
  transition: all 0.4s;
  transform: translateY(0px);
  opacity: 1;
}
.team1-box:hover .image img {
  filter: grayscale(1);
  transition: all 0.4s;
}

.progresbar-area {
  display: flex;
  align-items: center;
  justify-content: end;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .progresbar-area {
    justify-content: start;
    margin-top: 20px;
  }
}
@media (max-width: 767px) {
  .progresbar-area {
    justify-content: start;
    margin-top: 20px;
  }
}
.progresbar-area.right-after {
  position: relative;
}
.progresbar-area.right-after::after {
  content: "";
  position: absolute;
  top: 24px;
  right: -50px;
  height: 30px;
  width: 2px;
  background-color: var(--vtc-bg-common-2);
}

.circle-progress .progressbar {
  height: 80px;
}
.circle-progress .count {
  position: absolute;
  top: 30px;
  left: 20px;
  color: var(--vtc-text-sub-title-1);
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px; /* 100% */
}
.circle-progress p {
  color: var(--vtc-text-sub-title-1);
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px; /* 100% */
  padding-left: 16px;
}

.team2-box .image-area {
  background-color: var(--vtc-bg-common-9);
  border-radius: 16px;
  min-height: 340px;
  position: relative;
  text-align: center;
}
.team2-box .image-area .image {
  position: absolute;
  bottom: 0;
  display: flex;
  justify-content: center;
  left: auto;
  right: auto;
  width: 415px;
}
.team2-box .image-area .image img {
  transition: all 0.4s;
}
.team2-box .image-area .social-icons {
  position: absolute;
  right: 57px;
  bottom: 88px;
  transform: rotateX(45deg) translateY(75px);
  opacity: 0;
  transition: all 0.4s;
}
.team2-box .image-area .social-icons ul li a {
  display: inline-block;
  text-align: center;
  height: 44px;
  width: 44px;
  line-height: 44px;
  background-color: var(--vtc-bg-white1);
  border-radius: 50%;
  position: relative;
  z-index: 5;
  color: var(--vtc-text-title-4);
  font-size: var(--f-fs-font-18);
  margin: 3px 0px;
  transition: all 0.4s;
}
.team2-box .image-area .social-icons ul li a:hover {
  background-color: var(--vtc-bg-main4);
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
}
.team2-box .content-area {
  background-color: var(--vtc-bg-white6);
  padding: 28px 24px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: -70px 30px 0px 30px;
  position: relative;
  z-index: 4;
  border-radius: 16px;
  box-shadow: 0px 0px 40px 0px rgba(0, 0, 0, 0.09);
}
.team2-box .content-area .icon {
  background-color: var(--vtc-bg-main4);
  height: 50px;
  width: 50px;
  text-align: center;
  line-height: 50px;
  border-radius: 50%;
}
.team2-box .content-area .icon img {
  transition: all 0.4s;
}
.team2-box .content-area .heading h4 a {
  color: var(--vtc-text-title-3);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
}
.team2-box .content-area .heading p {
  color: var(--vtc-text-pera-4);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
  padding-top: 10px;
}
.team2-box:hover {
  transition: all 0.4s;
}
.team2-box:hover .image-area .social-icons {
  transform: rotateX(0deg) translateY(0);
  transition: all 0.4s;
  opacity: 1;
}
.team2-box:hover .image-area .image img {
  transition: all 0.4s;
  filter: grayscale(1);
}
.team2-box:hover .content-area .icon img {
  transition: all 0.4s;
  filter: brightness(40);
}
.team2-box:hover .content-area .heading h4 a:hover {
  color: var(--vtc-bg-main4);
}

.team3-box {
  text-align: center;
  transition: all 0.4s;
}
.team3-box .image-area {
  background-color: var(--vtc-bg-common-12);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  min-height: 260px;
}
.team3-box .image-area .image {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.team3-box .image-area .image img {
  transition: all 0.4s;
}
.team3-box .image-area .icons {
  position: absolute;
  bottom: 30px;
  left: 50%;
  margin-left: -95px;
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.4s;
}
.team3-box .image-area .icons ul li {
  display: inline-block;
}
.team3-box .image-area .icons ul li a {
  display: inline-block;
  color: var(--vtc-text-title-2);
  height: 40px;
  text-align: center;
  line-height: 40px;
  width: 40px;
  border-radius: 50%;
  background-color: var(--vtc-bg-white1);
  margin: 0px 2px;
  transition: all 0.4s;
}
.team3-box .image-area .icons ul li a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main6);
  color: var(--vtc-bg-white1);
}
.team3-box:hover {
  transform: translateY(-10px);
  transition: all 0.4s;
}
.team3-box:hover .icons {
  transition: all 0.4s;
  transform: translateY(0px);
  opacity: 1;
}
.team3-box:hover .image img {
  filter: grayscale(1);
  transition: all 0.4s;
}

.team4-box {
  text-align: center;
  transition: all 0.4s;
}
.team4-box .image-area {
  position: relative;
}
.team4-box .image-area .shape2 {
  position: absolute;
  left: 18px;
  top: -16px;
  transition: all 0.6s;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .team4-box .image-area .shape2 {
    left: 31px;
  }
}
.team4-box .image-area .image {
  overflow: hidden;
  border-radius: 50%;
  background-color: var(--vtc-bg-common-14);
  height: 240px;
  width: 240px;
  position: relative;
  margin: auto;
}
.team4-box .image-area .image .img {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 170px;
  margin-left: -85px;
  z-index: 2;
}
.team4-box .image-area .image .shape1 {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
  transform: scale(1.2);
  opacity: 0.2;
  transition: all 0.4s;
}
.team4-box .image-area .icon-area {
  position: absolute;
  bottom: 40px;
  z-index: 3;
  left: 50%;
  margin-left: -75px;
  transform: translateY(-50px);
  opacity: 0;
  transition: all 0.4s;
}
.team4-box .image-area .icon-area ul li {
  display: inline-block;
}
.team4-box .image-area .icon-area ul li a {
  display: inline-block;
  width: 44px;
  text-align: center;
  line-height: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  color: var(--vtc-bg-main8);
  margin: 0px 2px;
  transition: all 0.4s;
}
.team4-box .image-area .icon-area ul li a:hover {
  color: var(--vtc-bg-white1);
  background-color: var(--vtc-bg-main8);
  transition: all 0.4s;
}
.team4-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.team4-box:hover .image-area .shape1 {
  opacity: 1;
  transition: all 0.4s;
}
.team4-box:hover .image-area .shape2 {
  transform: rotate(62deg);
  transition: all 0.4s;
}
.team4-box:hover .image-area .icon-area {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.4s;
}

@media screen and (max-width: 426px) {
  .team4-box .image-area .shape2 {
    left: 65px;
  }
}
@media screen and (max-width: 376px) {
  .team4-box .image-area .shape2 {
    left: 40px;
  }
}
@media screen and (max-width: 321px) {
  .team4-box .image-area .shape2 {
    left: 12px;
  }
}
.team5-box {
  text-align: center;
  transition: all 0.4s;
}
.team5-box .image-area {
  background-color: var(--vtc-bg-common-17);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  min-height: 260px;
}
.team5-box .image-area .image {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.team5-box .image-area .image img {
  transition: all 0.4s;
}
.team5-box .image-area .icons {
  position: absolute;
  bottom: 30px;
  left: 50%;
  margin-left: -95px;
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.4s;
}
.team5-box .image-area .icons ul li {
  display: inline-block;
}
.team5-box .image-area .icons ul li a {
  display: inline-block;
  color: var(--vtc-text-title-2);
  height: 40px;
  text-align: center;
  line-height: 40px;
  width: 40px;
  border-radius: 50%;
  background-color: var(--vtc-bg-white1);
  margin: 0px 2px;
  transition: all 0.4s;
}
.team5-box .image-area .icons ul li a:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-main12);
  color: var(--vtc-bg-white1);
}
.team5-box:hover {
  transform: translateY(-10px);
  transition: all 0.4s;
}
.team5-box:hover .icons {
  transition: all 0.4s;
  transform: translateY(0px);
  opacity: 1;
}
.team5-box:hover .image img {
  filter: grayscale(1);
  transition: all 0.4s;
}

/*
::::::::::::::::::::::::::
 TEAM AREA CSS
::::::::::::::::::::::::::
*/
/*
 ::::::::::::::::::::::::::
  TESTIMONIAL AREA CSS
 ::::::::::::::::::::::::::
 */
.tes1 .left {
  position: relative;
}

.tes1 .left .swiper-button-next,
.tes1 .left .swiper-button-prev {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  z-index: 2;
  position: relative;
  text-align: center;
  display: inline-block;
  margin-top: 30px;
  left: 0;
  right: 0;
}

.tes1 .left .swiper-button-next::after,
.tes1 .left .swiper-button-prev::after {
  display: none;
}

.dark-mode .swiper-testimonial-2 .testimonial-boxarea .qute {
  filter: brightness(40);
}

.tes1 .pagination-buttons {
  text-align: center;
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea {
  border-radius: 18px;
  background: var(--vtc-bg-white3);
  padding: 36px;
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea .qute {
  position: absolute;
  right: 36px;
  top: 30px;
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea ul li {
  display: inline-block;
  height: 28px;
  width: 28px;
  text-align: center;
  line-height: 28px;
  border-radius: 2.203px;
  background: rgba(17, 17, 17, 0.06);
  color: #FB8500;
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea p {
  color: var(--vtc-text-pera-1);
  font-size: var(--f-fs-font-22);
  font-style: normal;
  font-weight: 500;
  line-height: var(--f-fs-font-32); /* 150% */
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea .names-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .tes1 .swiper-testimonial-2 .testimonial-boxarea .names-area {
    display: block;
  }
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea {
  display: flex;
  align-items: center;
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea .man img {
  height: 66px;
  width: 66px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea .text {
  padding-left: 16px;
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea .text a {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-22);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-22); /* 100% */
  transition: all 0.4s;
  display: inline-block;
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea .text a:hover {
  color: var(--ztc-text-text-8);
  transition: all 0.4s;
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea .text p {
  color: var(--vtc-text-pera-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
}
.tes1 .swiper-testimonial-2 .testimonial-boxarea .names-area .elements20 {
  width: 160px;
  height: 40px;
  -o-object-fit: contain;
     object-fit: contain;
}
@media (max-width: 767px) {
  .tes1 .swiper-testimonial-2 .testimonial-boxarea .names-area .elements20 {
    margin-top: 16px;
  }
}
.tes1 .map-testimonial {
  position: absolute;
  width: 100%;
  height: 400px;
  bottom: 136px;
}
.tes1 .map-testimonial .swiper.swiper-thumb2 {
  overflow: inherit !important;
}

.tes1 .map-testimonial .swiper-slide div {
  width: 82px;
  height: 82px;
  border-radius: 50%;
  position: relative;
}
@media (max-width: 767px) {
  .tes1 .map-testimonial .swiper-slide div {
    display: none;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tes1 .map-testimonial .swiper-slide div {
    display: none;
  }
}

.tes1 .map-testimonial .swiper-slide div::before {
  content: "";
  position: absolute;
  top: -4px;
  left: -4px;
  width: 88px;
  height: 88px;
  border-radius: 50%;
  background: var(--vtc-bg-main3);
  opacity: 0;
  z-index: -1;
}

.tes1 .map-testimonial .swiper-slide div img {
  width: 80px;
  height: 80px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
}

.tes1 .map-testimonial .swiper-slide.swiper-slide-thumb-active div::before {
  opacity: 1;
}

.tes1 .map-testimonial .swiper-slide:nth-child(1) {
  bottom: 5%;
  left: 100px;
}

.tes1 .map-testimonial .swiper-slide:nth-child(2) {
  top: 120px;
  left: 50px;
  position: absolute;
}

.tes1 .map-testimonial .swiper-slide:nth-child(3) {
  top: 240px;
  left: 100px;
  position: absolute;
}

.tes1 .map-testimonial .swiper-slide:nth-child(4) {
  bottom: 5%;
  left: 1120px;
  position: absolute;
}

.tes1 .map-testimonial .swiper-slide:nth-child(5) {
  top: 120px;
  left: 1160px;
  position: absolute;
}

.tes1 .map-testimonial .swiper-slide:nth-child(6) {
  top: 240px;
  left: 1120px;
  position: absolute;
}

.tes1-arrows {
  margin-right: 10px;
  text-align: center;
}
.tes1-arrows button {
  display: inline-block;
  border: none;
  border-radius: 50%;
  height: 56px;
  width: 56px;
  text-align: center;
  line-height: 56px;
  background-color: var(--vtc-bg-main3);
  font-size: var(--f-fs-font-20);
  color: var(--vtc-bg-white2);
  transition: all 0.4s;
  margin-right: 5px;
}
.tes1-arrows button:hover {
  background-color: var(--vtc-bg-main1);
  color: var(--vtc-text-title-1);
}

.testimonial-boxarea .white-logo {
  display: none;
}

.dark-mode .testimonial-boxarea .white-logo {
  display: block;
}
.dark-mode .testimonial-boxarea .black-logo {
  display: none;
}

.tes2 {
  position: relative;
  z-index: 1;
}
.tes2 .img1 img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  border-radius: 16px;
}
.tes2 .slider-galeria .testimonial-slider-content-area {
  position: relative;
  z-index: 1;
  margin: 0 30px;
}
.tes2 .slider-galeria .testimonial-slider-content-area .quito1 {
  filter: brightness(0) invert(1);
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-author-area {
  position: relative;
  z-index: 1;
  background: var(--vtc-bg-main5);
  border-radius: 8px;
  padding: 28px 38px 28px 28px;
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-author-area .quito1 {
  position: absolute;
  right: 20px;
  top: 20px;
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-author-area ul li {
  display: inline-block;
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-author-area ul li a {
  height: 26px;
  width: 26px;
  text-align: center;
  line-height: 26px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.4s;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.1);
  color: #FFA800;
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-author-area p {
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-26); /* 144.444% */
  transition: all 0.4s;
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-author-area .elements18 {
  position: absolute;
  bottom: -35px;
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-man-info-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-man-info-area .mans-img img {
  height: 60px;
  width: 60px;
  text-align: center;
  line-height: 60px;
  border-radius: 50%;
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-man-info-area .man-images-text {
  display: flex;
  align-items: center;
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-man-info-area .man-images-text .man-text a {
  color: var(--vtc-text-title-3);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
  display: inline-block;
  transition: all 0.4s;
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-man-info-area .man-images-text .man-text p {
  color: var(--vtc-text-pera-4);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
  transition: all 0.4s;
}

@media (max-width: 767px) {
  .tes2 .slider-galeria .testimonial-slider-content-area {
    margin: 30px 0 0 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tes2 .slider-galeria .testimonial-slider-content-area {
    margin: 30px 0 0 0;
  }
}
.tes2 .slider-galeria .testimonial-slider-content-area .testimonial-man-info-area .man-images-text .man-text {
  padding-left: 16px;
}

.tes2 .slider-galeria-thumbs .testimonial3-sliders-img {
  position: relative;
}

.tes2 .slider-galeria-thumbs .testimonial3-sliders-img.slick-slide.slick-current.slick-active:after {
  position: absolute;
  content: "";
  height: 74px;
  width: 74px;
  background: var(--vtc-bg-main5);
  z-index: -1;
  top: -1px;
  left: -2px;
  border-radius: 50%;
}

.tes2 .slider-galeria-thumbs .testimonial3-sliders-img img {
  height: 70px;
  width: 70px;
  border-radius: 50%;
  -o-object-fit: cover;
  object-fit: cover;
  transition: all 0.4s;
  margin: 0 0 10px 0;
  cursor: pointer;
}

.tes3 {
  position: relative;
  z-index: 1;
  background: var(--Home-Page-9-Gray-Color, #F2F5FF);
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tes3 .slider-area {
    margin-top: 40px;
  }
}
@media (max-width: 767px) {
  .tes3 .slider-area {
    margin-top: 40px;
  }
}
.tes3 .testimonial-horizental-slider2 {
  margin-top: 0px;
}
.tes3 .testimonial-vertical {
  background-color: var(--vtc-bg-white1);
  padding: 32px;
  border-radius: 16px;
}
.tes3 .testimonial-vertical .stars {
  padding-bottom: 16px;
}
.tes3 .testimonial-vertical .stars ul li {
  color: #FB8500;
  display: inline-block;
  padding: 0px 2px;
  border-radius: 2px;
  background: rgba(17, 17, 17, 0.06);
  padding: 2px 4px 2px 4px;
  margin: 0px 2px;
}
.tes3 .testimonial-vertical p {
  color: rgba(17, 17, 17, 0.8);
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 30px; /* 150% */
}
.tes3 .testimonial-vertical .bottom-area-all {
  justify-content: space-between;
  display: flex;
  align-items: center;
  padding-top: 24px;
}
@media (max-width: 767px) {
  .tes3 .testimonial-vertical .bottom-area-all .brand-logo {
    margin-top: 20px;
  }
}
@media (max-width: 767px) {
  .tes3 .testimonial-vertical .bottom-area-all {
    display: block;
  }
}
.tes3 .testimonial-vertical .bottom-area-all .author-area {
  display: flex;
  align-items: center;
}
.tes3 .testimonial-vertical .bottom-area-all .text {
  padding-left: 20px;
}
.tes3 .testimonial-vertical .bottom-area-all .text h4 a {
  color: var(--Home-Page-2-Color-Text-Color, #0D0E1F);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 100% */
}
.tes3 .testimonial-vertical .bottom-area-all .text p {
  font-size: 16px;
}
.tes3 .slider-boxarea2 .slider-box {
  display: flex;
  align-items: center;
  border-radius: 16px;
  background: var(--vtc-bg-white1);
  padding: 20px;
  margin-bottom: 20px;
  margin-right: 30px;
}
.tes3 .slider-boxarea2 .slider-box .img1 img {
  height: 50px;
  width: 50px;
  border-radius: 50%;
  -o-object-fit: cover;
  object-fit: cover;
}
.tes3 .slider-boxarea2 .slider-box .content a {
  color: var(--Home-Page-3-Color-Text-Color, #061D19);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 100% */
  display: inline-block;
  transition: all 0.4s;
}
.tes3 .slider-boxarea2 .slider-box .content p {
  color: rgba(17, 17, 17, 0.8);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 100% */
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tes3 .slider-boxarea2 .slider-box {
    margin-right: 200px;
  }
}
@media (max-width: 767px) {
  .tes3 .slider-boxarea2 .slider-box {
    margin-right: 0;
  }
}
.tes3 .slider-boxarea2 .slider-box .content {
  padding-left: 12px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tes3 .testimonial-horizental-slider2 {
    margin-top: 30px;
  }
}
@media (max-width: 767px) {
  .tes3 .testimonial-horizental-slider2 {
    margin-top: 30px;
  }
}
.tes3 .testimonial-horizental-slider2 .testimonial-vertical .verical-boxarea {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: 767px) {
  .tes3 .testimonial-horizental-slider2 .testimonial-vertical .verical-boxarea {
    display: inline-block;
  }
}
@media (max-width: 767px) {
  .tes3 .testimonial-horizental-slider2 .testimonial-vertical .verical-boxarea .quito {
    margin-top: 20px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tes3 .tes3-arrows {
    position: absolute;
    right: 50px;
    top: 50%;
    margin-top: -75px;
    left: inherit;
  }
}
@media (max-width: 767px) {
  .tes3 .testimonial-arrows {
    position: relative;
    right: 0;
    left: 0;
    top: 0;
    text-align: center;
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
}
@media (max-width: 767px) {
  .tes3 .testimonial-arrows .prev-arrow {
    margin-bottom: 0;
    margin: 0 16px 0 0;
  }
}
.tes3-arrows {
  margin-top: 40px;
  margin-right: 10px;
  position: absolute;
  right: -90px;
  top: 50%;
  margin-top: -65px;
}
.tes3-arrows button {
  display: inline-block;
  border: none;
  border-radius: 8%;
  height: 56px;
  width: 56px;
  text-align: center;
  line-height: 56px;
  background-color: rgba(133, 176, 60, 0.3490196078);
  font-size: var(--f-fs-font-20);
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
  margin-right: 5px;
}
.tes3-arrows button:hover {
  background-color: var(--vtc-bg-main6);
  color: var(--vtc-bg-white1);
}
.tes3-arrows .next-arrow {
  margin-top: 16px;
}

.tes4-single-slider {
  background-color: var(--vtc-bg-common-19);
  padding: 24px;
  border-radius: 8px;
  margin: 0px 10px;
}
.tes4-single-slider .stars {
  padding-top: 16px;
}
.tes4-single-slider .stars ul li {
  color: #FFA800;
  margin: 0px 2px;
  display: inline-block;
}
.tes4-single-slider p {
  color: var(--vtc-text-title-2);
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 28px; /* 155.556% */
  padding-top: 16px;
}
.tes4-single-slider .bottom-area {
  display: flex;
  align-items: center;
  border-top: 1px solid var(--vtc-border-2);
  margin-top: 24px;
  padding-top: 24px;
}
.tes4-single-slider .bottom-area .text {
  padding-left: 16px;
}
.tes4-single-slider .bottom-area .text h4 a {
  color: var(--vtc-text-title-2);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
  transition: all 0.4s;
}
.tes4-single-slider .bottom-area .text h4 a:hover {
  color: var(--vtc-bg-main8);
  transition: all 0.4s;
}
.tes4-single-slider .bottom-area .text p {
  color: var(--vtc-text-pera-8);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
  padding-top: 10px;
}

.tes5 .left .swiper-button-next,
.tes5 .left .swiper-button-prev {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  z-index: 2;
  position: relative;
  text-align: center;
  display: inline-block;
  margin-top: 30px;
  left: 0;
  right: 0;
}

.tes5 .left .swiper-button-next::after,
.tes5 .left .swiper-button-prev::after {
  display: none;
}

.tes5 .pagination-buttons {
  text-align: center;
}
.tes5 .swiper-testimonial-2 {
  box-shadow: 0px 4px 30px 0px rgba(0, 0, 0, 0.09);
  border-radius: 16px;
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea {
  border-radius: 18px;
  background: var(--vtc-bg-white9);
  padding: 36px;
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea .qute {
  position: absolute;
  right: 36px;
  top: 30px;
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea ul li {
  display: inline-block;
  height: 28px;
  width: 28px;
  text-align: center;
  line-height: 28px;
  border-radius: 2.203px;
  background: rgba(17, 17, 17, 0.06);
  color: #FB8500;
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea p {
  color: var(--vtc-text-pera-1);
  font-size: var(--f-fs-font-22);
  font-style: normal;
  font-weight: 500;
  line-height: var(--f-fs-font-32); /* 150% */
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea .names-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 767px) {
  .tes5 .swiper-testimonial-2 .testimonial-boxarea .names-area {
    display: block;
  }
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea {
  display: flex;
  align-items: center;
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea .man img {
  height: 66px;
  width: 66px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea .text {
  padding-left: 16px;
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea .text a {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-22);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-22); /* 100% */
  transition: all 0.4s;
  display: inline-block;
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea .text a:hover {
  color: var(--ztc-text-text-8);
  transition: all 0.4s;
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea .names-area .man-textarea .text p {
  color: var(--vtc-text-pera-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
}
.tes5 .swiper-testimonial-2 .testimonial-boxarea .names-area .elements20 {
  width: 160px;
  height: 40px;
  -o-object-fit: contain;
     object-fit: contain;
}
@media (max-width: 767px) {
  .tes5 .swiper-testimonial-2 .testimonial-boxarea .names-area .elements20 {
    margin-top: 16px;
  }
}
.tes5 .map-testimonial {
  position: absolute;
  width: 100%;
  height: 400px;
  bottom: 136px;
}
.tes5 .map-testimonial .swiper.swiper-thumb2 {
  overflow: inherit !important;
}

.tes5 .map-testimonial .swiper-slide div {
  width: 82px;
  height: 82px;
  border-radius: 50%;
  position: relative;
}
@media (max-width: 767px) {
  .tes5 .map-testimonial .swiper-slide div {
    display: none;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .tes5 .map-testimonial .swiper-slide div {
    display: none;
  }
}

.tes5 .map-testimonial .swiper-slide div::before {
  content: "";
  position: absolute;
  top: -4px;
  left: -4px;
  width: 88px;
  height: 88px;
  border-radius: 50%;
  background: var(--vtc-bg-main10);
  opacity: 0;
  z-index: -1;
}

.tes5 .map-testimonial .swiper-slide div img {
  width: 80px;
  height: 80px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 50%;
}

.tes5 .map-testimonial .swiper-slide.swiper-slide-thumb-active div::before {
  opacity: 1;
}

.tes5 .map-testimonial .swiper-slide:nth-child(1) {
  bottom: 5%;
  left: 100px;
}

.tes5 .map-testimonial .swiper-slide:nth-child(2) {
  top: 120px;
  left: 50px;
  position: absolute;
}

.tes5 .map-testimonial .swiper-slide:nth-child(3) {
  top: 240px;
  left: 100px;
  position: absolute;
}

.tes5 .map-testimonial .swiper-slide:nth-child(4) {
  bottom: 5%;
  left: 1120px;
  position: absolute;
}

.tes5 .map-testimonial .swiper-slide:nth-child(5) {
  top: 120px;
  left: 1160px;
  position: absolute;
}

.tes5 .map-testimonial .swiper-slide:nth-child(6) {
  top: 240px;
  left: 1120px;
  position: absolute;
}

.tes5-arrows {
  margin-right: 10px;
  text-align: center;
}
.tes5-arrows button {
  display: inline-block;
  border: none;
  border-radius: 50%;
  height: 56px;
  width: 56px;
  text-align: center;
  line-height: 56px;
  background-color: var(--vtc-bg-common-17);
  font-size: var(--f-fs-font-20);
  color: var(--vtc-text-title-7);
  transition: all 0.4s;
  margin-right: 5px;
}
.tes5-arrows button:hover {
  background-color: var(--vtc-bg-main12);
  color: var(--vtc-bg-white1);
}

.testimonial-boxarea .white-logo {
  display: none;
}

.dark-mode .testimonial-boxarea .white-logo {
  display: block;
}
.dark-mode .testimonial-boxarea .black-logo {
  display: none;
}

.tes-page-box {
  background-color: var(--vtc-bg-common-2);
  border-radius: 8px;
  padding: 24px;
  transition: all 0.4s;
}
.tes-page-box .stars {
  padding-top: 8px;
}
.tes-page-box .stars ul li {
  display: inline-block;
  color: #FFA800;
  margin: 0px 2px;
}
.tes-page-box p {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-28); /* 155.556% */
  padding-top: 16px;
}
.tes-page-box .bottom-area {
  display: flex;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid var(--vtc-border-1);
  margin-top: 20px;
}
.tes-page-box .bottom-area .text {
  padding-left: 16px;
}
.tes-page-box .bottom-area .text a {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
}
.tes-page-box .bottom-area .text p {
  color: var(--vtc-text-pera-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
  padding-top: 8px;
}
.tes-page-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}

/*
::::::::::::::::::::::::::
 WORK AREA CSS
::::::::::::::::::::::::::
*/
.work1-box {
  transition: all 0.4s;
}
.work1-box .image {
  overflow: hidden;
  border-radius: 50%;
  margin: 0px 40px;
}
.work1-box .image img {
  width: 100%;
  border-radius: 50%;
  transition: all 0.4s;
}
.work1-box .content-area .number {
  color: var(--vtc-bg-white2);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
  background-color: var(--vtc-bg-main3);
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  margin: 20px 0px 16px 0px;
  transition: all 0.4s;
}
.work1-box:hover {
  color: var();
  transition: all 0.4s;
  transform: translateY(-10px);
}
.work1-box:hover .number {
  transition: all 0.4s;
  color: var(--vtc-bg-white2);
  background-color: var(--vtc-bg-main1);
}
.work1-box:hover .image img {
  transition: all 0.4s;
  transform: scale(1.1) rotate(2deg);
  filter: grayscale(1);
}

.work1-border {
  position: relative;
}
.work1-border::after {
  content: "";
  position: absolute;
  bottom: 150px;
  left: 150px;
  height: 1px;
  width: 76%;
  background-color: var(--vtc-bg-common-2);
  z-index: -1;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .work1-border::after {
    display: none;
  }
}
@media (max-width: 767px) {
  .work1-border::after {
    display: none;
  }
}

.work2-box {
  text-align: center;
  position: relative;
  transition: all 0.4s;
  margin-left: 10px;
  margin-right: 10px;
}
.work2-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.work2-box .image {
  margin: 0px 90px;
  position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .work2-box .image {
    margin: 0px 48px;
  }
}
@media (max-width: 767px) {
  .work2-box .image {
    margin: 0px 48px;
  }
}
.work2-box .image img {
  width: 100%;
  border-radius: 8px;
  transition: all 0.4s;
}
.work2-box .image .number {
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  background-color: var(--vtc-bg-main5);
  border-radius: 50%;
  position: absolute;
  top: -68px;
  left: 50%;
  margin-left: -22px;
  transition: all 0.4s;
}
.work2-box .image::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  height: 220px;
  width: 220px;
  border: 2px solid #CFCFD2;
  border-radius: 8px;
  transform: rotate(-45deg) translateY(-5px);
  z-index: -1;
}
.work2-box .content {
  background-color: var(--vtc-bg-common-6);
  padding: 20px 44px;
  border-radius: 8px;
  transition: all 0.4s;
}
.work2-box:hover .content {
  background-color: var(--vtc-bg-main4);
  transition: all 0.4s;
}
.work2-box:hover .content a {
  transition: all 0.4s;
  color: var(--vtc-bg-white1);
}
.work2-box:hover .content p {
  color: var(--vtc-bg-white1);
  transition: all 0.4s;
}
.work2-box:hover .number {
  background-color: var(--vtc-bg-main4);
  transition: all 0.4s;
}
.work2-box:hover .image img {
  transition: all 0.4s;
  filter: grayscale(1);
}

@media screen and (max-width: 426px) {
  .work2-box .image {
    margin: 0px 82px;
  }
}
@media screen and (max-width: 376px) {
  .work2-box .image {
    margin: 0px 57px;
  }
}
@media screen and (max-width: 321px) {
  .work2-box .image {
    margin: 0px 31px;
  }
}
.work-bxs-sec {
  position: relative;
}
.work-bxs-sec::after {
  position: absolute;
  content: "";
  bottom: 130px;
  left: 0;
  height: 1px;
  width: 100%;
  background-color: var(--vtc-text-pera-4);
  opacity: 0.4;
  transform: rotateY(43deg);
  z-index: -1;
}

.work3-box .number {
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  background-color: var(--vtc-bg-main7);
  border-radius: 50%;
  margin: auto;
  transition: all 0.4s;
  position: relative;
  z-index: 2;
}
.work3-box .image {
  margin: 0px 30px;
  overflow: hidden;
  border-radius: 16px;
  margin-bottom: 30px;
}
.work3-box .image img {
  width: 100%;
  transition: all 0.4s;
}
.work3-box:hover {
  transition: all 0.4s;
}
.work3-box:hover .number {
  background-color: var(--vtc-bg-main6);
  transition: all 0.4s;
}
.work3-box:hover .image img {
  transition: all 0.4s;
  transform: scale(1.1) rotate(2deg);
  filter: grayscale(1);
}

.work4-box {
  padding: 30px 20px 0px 20px;
  text-align: center;
  transition: all 0.4s;
}
.work4-box .image-area {
  background-color: var(--vtc-bg-common-14);
  border-radius: 50%;
  height: 240px;
  width: 240px;
  padding: 8px;
  margin: auto;
}
.work4-box .image-area .image {
  overflow: hidden;
  border-radius: 50%;
  position: relative;
}
.work4-box .image-area .image img {
  width: 100%;
  transition: all 0.4s;
}
.work4-box .image-area .image .number {
  height: 80px;
  width: 80px;
  border-radius: 50%;
  text-align: center;
  background: var(--vtc-bg-main9);
  position: absolute;
  bottom: -34px;
  left: 50%;
  margin-left: -40px;
  transition: all 0.4s;
}
.work4-box .image-area .image .number p {
  color: var(--vtc-bg-white7);
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 90.909% */
  padding-top: 15px;
}
.work4-box:hover {
  transform: translateY(-10px);
  transition: all 0.4s;
}
.work4-box:hover .image-area .image img {
  transform: scale(1.1) rotate(2deg);
}
.work4-box:hover .image-area .image .number {
  background-color: var(--vtc-bg-main8);
  transition: all 0.4s;
}

.work5-box {
  background-color: var(--vtc-bg-white9);
  border-radius: 16px;
  padding: 40px 45px 32px 45px;
  text-align: center;
  transition: all 0.4s;
}
.work5-box .content {
  padding-top: 36px;
}
.work5-box .content h4 a {
  color: var(--vtc-text-title-7);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 100% */
  transition: all 0.4s;
}
.work5-box .content h4 a:hover {
  color: var(--vtc-bg-main12);
  transition: all 0.4s;
}
.work5-box .content p {
  color: var(--vtc-text-pera-7);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-26); /* 144.444% */
  padding-top: 16px;
}
.work5-box .icon-area {
  position: relative;
  height: 100px;
  width: 100px;
  margin: auto;
}
.work5-box .icon-area .icon {
  height: 100px;
  width: 100px;
  text-align: center;
  line-height: 100px;
  background: var(--vtc-bg-main10);
  border-radius: 50%;
  margin: auto;
  position: relative;
  z-index: 2;
}
.work5-box .icon-area .icon::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 1px solid var(--vtc-border-2);
  transform: scale(1.2);
}
.work5-box .icon-area .icon img {
  transition: all 0.4s;
}
.work5-box .icon-area .number {
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  background: var(--vtc-bg-main10);
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  position: absolute;
  top: -4px;
  right: -16px;
  z-index: 3;
}
.work5-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}
.work5-box:hover .icon-area .icon img {
  transition: all 0.4s;
  transform: rotateY(180deg);
}

/*
 ::::::::::::::::::::::::::
  WORK AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 others AREA CSS
::::::::::::::::::::::::::
*/
.consultation {
  padding: 80px 0px;
}
.consultation .consultation-box {
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  padding: 24px;
  display: flex;
  align-items: center;
}
.consultation .consultation-box .icon {
  height: 60px;
  width: 60px;
  text-align: center;
  line-height: 60px;
  background-color: var(--vtc-bg-main4);
  border-radius: 50%;
}
.consultation .consultation-box .text {
  padding-left: 16px;
}
.consultation .consultation-box .text p {
  color: var(--vtc-text-pera-3);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-normal);
  line-height: var(--f-fs-font-16); /* 100% */
}
.consultation .consultation-box .text a {
  display: inline-block;
  color: var(--vtc-bg-white1);
  font-size: var(--f-fs-font-20);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-20); /* 100% */
  transition: all 0.4s;
  padding-top: 10px;
}
.consultation .consultation-box .text a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main4);
}

/*
 ::::::::::::::::::::::::::
  others AREA CSS
 ::::::::::::::::::::::::::
 */
/*
::::::::::::::::::::::::::
 CONTACT AREA CSS
::::::::::::::::::::::::::
*/
.contact4 {
  overflow: hidden;
}
.contact4 .contact4-form-area {
  padding: 48px;
  border-radius: 16px;
  background-color: var(--vtc-bg-white6);
}
.contact4 .contact4-form-area h3 {
  color: var(--vtc-text-title-5);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 100% */
}
.contact4 .contact4-form-area p {
  color: var(--vtc-text-pera-7);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
  margin-top: 10px;
}
.contact4 .contact4-form-area .form form {
  padding-top: 10px;
}
.contact4 .contact4-form-area .form .single-input {
  margin-top: 20px;
}
.contact4 .contact4-form-area .form .single-input input, .contact4 .contact4-form-area .form .single-input textarea {
  padding: 16px;
  border-radius: 8px;
  border: none;
  background-color: var(--vtc-bg-common-14);
  width: 100%;
}
.contact4 .contact4-form-area .form .single-input input::-moz-placeholder, .contact4 .contact4-form-area .form .single-input textarea::-moz-placeholder {
  color: var(--vtc-text-title-5);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
  width: 100%;
  opacity: 0.6;
}
.contact4 .contact4-form-area .form .single-input input::placeholder, .contact4 .contact4-form-area .form .single-input textarea::placeholder {
  color: var(--vtc-text-title-5);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-18); /* 100% */
  width: 100%;
  opacity: 0.6;
}
.contact4 .contact4-form-area .form .single-input input:focus, .contact4 .contact4-form-area .form .single-input textarea:focus {
  outline: none;
}
.contact4 .contact4-form-area .form .nice-select.wide {
  background-color: var(--vtc-bg-common-14);
  width: 100%;
  border: none;
  color: var(--vtc-text-title-5);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: 54px; /* 100% */
  width: 100%;
  opacity: 0.6;
  height: 54px;
}
.contact4 .shape {
  position: absolute;
  right: 0;
  top: 0;
}

.nice-select:after {
  border-bottom: 2px solid #999;
  border-right: 2px solid #999;
  content: "";
  display: block;
  height: 7px;
  margin-top: -4px;
  pointer-events: none;
  position: absolute;
  right: 12px;
  top: 50%;
  transform-origin: 66% 66%;
  transform: rotate(45deg);
  transition: all 0.15s ease-in-out;
  width: 7px;
}

.contact4-box {
  display: flex;
  align-items: center;
  background-color: var(--vtc-bg-common-14);
  border-radius: 8px;
  padding: 24px;
  margin-left: 30px;
  transition: all 0.4s;
}
.contact4-box .icon {
  background-color: var(--vtc-bg-main8);
  height: 80px;
  width: 80px;
  text-align: center;
  line-height: 80px;
  border-radius: 50%;
}
.contact4-box .icon img {
  filter: brightness(0) invert(1);
  transition: all 0.4s;
}
.contact4-box .text {
  padding-left: 20px;
}
.contact4-box .text h4 {
  color: var(--vtc-text-title-5);
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; /* 100% */
}
.contact4-box .text a {
  display: inline-block;
  color: var(--vtc-text-pera-7);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 26px; /* 162.5% */
  padding-top: 12px;
}
.contact4-box:hover {
  transform: translateY(-10px);
  transition: all 0.4s;
  background-color: var(--vtc-bg-main8);
}
.contact4-box:hover .icon {
  background-color: var(--vtc-bg-white1);
  transition: all 0.4s;
}
.contact4-box:hover .icon img {
  transition: all 0.4s;
  filter: none;
  transform: rotateY(180deg);
}
.contact4-box:hover .text h4 {
  transition: all 0.4s;
  color: var(--vtc-bg-white1);
}
.contact4-box:hover .text a {
  transition: all 0.4s;
  color: var(--vtc-bg-white1);
}

.contact5 {
  /* Thumb styling */
}
.contact5 .images-all {
  position: relative;
  height: 640px;
  text-align: end;
  margin-top: 30px;
}
.contact5 .images-all .image2 {
  position: absolute;
  bottom: -10px;
}
.contact5 .images-all .shape {
  position: absolute;
  top: 19px;
  left: 64px;
}
.contact5 .contact-form {
  background: var(--vtc-bg-white1);
  padding: 32px;
  border-radius: 16px;
  margin-left: 40px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .contact5 .contact-form {
    margin-left: 0;
  }
}
@media (max-width: 767px) {
  .contact5 .contact-form {
    margin-left: 0;
  }
}
.contact5 .contact-form h3 {
  color: var(--vtc-text-title-7);
  font-size: var(--f-fs-font-24);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-24); /* 100% */
}
.contact5 .contact-form p {
  color: var(--vtc-text-pera-8);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-16); /* 100% */
  padding-top: 12px;
}
.contact5 .categories {
  margin-bottom: 24px;
  margin-top: 24px;
}
.contact5 .categories button {
  display: inline-block;
}
.contact5 .category {
  flex: 1;
  padding: 16px 22px;
  margin: 0 5px;
  border-radius: 8px;
  background: var(--vtc-bg-common-18);
  cursor: pointer;
  border: none;
  color: var(--vtc-text-title-8);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
}
.contact5 .category.active {
  background: var(--vtc-bg-main10);
  color: var(--vtc-bg-white1);
}
.contact5 .category:hover {
  background: var(--vtc-bg-main10);
  color: var(--vtc-bg-white1);
}
.contact5 .form-group {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.contact5 p.selected-value {
  position: absolute;
  right: 0;
  top: -8px;
  color: var(--vtc-text-title-8);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
}
.contact5 .input-field {
  width: 48%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  background-color: var(--vtc-bg-common-18);
  font-size: 16px;
}
.contact5 .input-field::-moz-placeholder {
  color: var(--vtc-text-pera-8);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
}
.contact5 .input-field::placeholder {
  color: var(--vtc-text-pera-8);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
}
.contact5 .input-field:focus {
  outline: none;
}
.contact5 .textarea-field {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  background-color: var(--vtc-bg-common-18);
  font-size: var(--f-fs-font-16);
  margin-bottom: 20px;
  resize: none;
}
.contact5 .textarea-field::-moz-placeholder {
  color: var(--vtc-text-pera-8);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
}
.contact5 .textarea-field::placeholder {
  color: var(--vtc-text-pera-8);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
}
.contact5 .textarea-field:focus {
  outline: none;
}
.contact5 .slider-container {
  text-align: left;
  margin-bottom: 20px;
}
.contact5 .slider-container label {
  color: var(--vtc-text-title-8);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
}
.contact5 .slider {
  width: 100%;
  margin: 10px 0;
}
.contact5 .slider-values {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}
.contact5 .selected-value {
  font-size: 14px;
  color: #333;
}
.contact5 .submit-btn {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 5px;
  background: #4d3ebf;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}
.contact5 .submit-btn:hover {
  background: #3a2eb2;
}
.contact5 .slider-container {
  margin: 0 auto;
  text-align: left;
}
.contact5 .slider-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: bold;
}
.contact5 #balance-slider {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  height: 10px;
  border-radius: 5px;
  background: linear-gradient(90deg, #2E0797 0%, #726EFC 100%);
  outline: none;
  transition: background 0.3s ease;
}
.contact5 #balance-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(90deg, #2E0797 0%, #726EFC 100%);
  cursor: pointer;
  box-shadow: 0 0 5px rgba(1, 255, 77, 0.5);
}
.contact5 #balance-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(90deg, #2E0797 0%, #726EFC 100%);
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}
.contact5 .selected-value {
  margin-top: 10px;
  font-size: 14px;
}

.contact-page-sec .images-all {
  position: relative;
  height: 536px;
  text-align: end;
}
.contact-page-sec .images-all .image2 {
  position: absolute;
  bottom: 0;
  left: 60px;
}
.contact-page-sec .images-all .shape {
  position: absolute;
  top: 0;
  left: 100px;
}
.contact-page-sec .contact-page-boxs .contact-page-box {
  background-color: var(--vtc-bg-common-2);
  padding: 24px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  margin-top: 40px;
  transition: all 0.4s;
}
.contact-page-sec .contact-page-boxs .contact-page-box .icon {
  background-color: var(--vtc-bg-main3);
  height: 60px;
  width: 60px;
  text-align: center;
  line-height: 60px;
  border-radius: 50%;
  transition: all 0.4s;
}
.contact-page-sec .contact-page-boxs .contact-page-box .icon img {
  transition: all 0.4s;
  filter: brightness(40);
}
.contact-page-sec .contact-page-boxs .contact-page-box .text {
  text-align: start;
  padding-left: 10px;
}
.contact-page-sec .contact-page-boxs .contact-page-box .text h4 {
  color: var(--vtc-text-title-1);
  font-size: var(--f-fs-font-18);
  font-style: normal;
  font-weight: var(--f-fw-semibold);
  line-height: var(--f-fs-font-18); /* 100% */
}
.contact-page-sec .contact-page-boxs .contact-page-box .text a {
  color: var(--vtc-text-pera-1);
  font-size: var(--f-fs-font-16);
  font-style: normal;
  font-weight: var(--f-fw-medium);
  line-height: var(--f-fs-font-16); /* 100% */
  transition: all 0.4s;
  display: inline-block;
  padding-top: 10px;
}
.contact-page-sec .contact-page-boxs .contact-page-box .text a:hover {
  transition: all 0.4s;
  color: var(--vtc-bg-main1);
}
.contact-page-sec .contact-page-boxs .contact-page-box:hover {
  transition: all 0.4s;
  transform: translateY(-10px);
}

.contact-map-page iframe {
  width: 100%;
  border-radius: 16px;
}

.dark-mode .contact-page-sec .images-all .shape {
  filter: brightness(40);
}
.dark-mode .contact-page-sec .contact-page-boxs .contact-page-box .icon img {
  filter: none;
}

/*
 ::::::::::::::::::::::::::
  CONTACT AREA CSS
 ::::::::::::::::::::::::::
 *//*# sourceMappingURL=main.css.map */

.slick-track
{
    display: flex !important;
}

.slick-slide
{
    height: inherit !important;
}

/*.service-details-area .service1-single-slider:not(:hover) {*/
/*  background-color: var(--vtc-bg-common-2);*/
/*}*/

.service1-single-slider.service-reversed {
  background-color: var(--vtc-bg-main3);
  padding: 32px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  transition: all 0.4s;
  margin: 0px 10px;
}
.service1-single-slider.service-reversed .icon {
  height: 70px;
  width: 70px;
  text-align: center;
  line-height: 70px;
  background-color: var(--vtc-bg-white3);
  border-radius: 50%;
  margin-bottom: 20px;
  transition: all 0.4s;
  display: inline-flex;
}
.service1-single-slider.service-reversed .icon img {
  transition: all 0.4s;
  filter: none;
  height: 41px;
  margin: auto;
}
.service1-single-slider.service-reversed .arrow {
  display: inline-block;
  height: 40px;
  width: 40px;
  text-align: center;
  line-height: 40px;
  color: var(--vtc-bg-white2);
  background-color: var(--vtc-bg-main3);
  border-radius: 50%;
  transform: rotate(-45deg);
  position: absolute;
  right: -50px;
  top: -50px;
  transition: all 0.4s;
}
.service1-single-slider.service-reversed .number {
  position: relative;
  padding-left: 76px;
}
.service1-single-slider.service-reversed .number::after {
  content: "";
  position: absolute;
  left: 0;
  top: 12px;
  height: 1px;
  width: 70px;
  background-color: var(--vtc-bg-white3);
  transition: all 0.4s;
}
.service1-single-slider.service-reversed h4 a {
  color: var(--vtc-bg-white3);
  transition: all 0.4s;
}
.service1-single-slider.service-reversed p {
  transition: all 0.4s;
  color: var(--vtc-bg-white3);
}
.service1-single-slider.service-reversed:hover {
  transition: all 0.4s;
  background-color: var(--vtc-bg-white3);
}
.service1-single-slider.service-reversed:hover h4 a {
  color: var(--vtc-bg-main3);
  transition: all 0.4s;
}
.service1-single-slider.service-reversed:hover .arrow {
  top: 16px;
  right: 16px;
}
.service1-single-slider.service-reversed:hover p {
  transition: all 0.4s;
  color: var(--vtc-bg-main3);
}
.service1-single-slider.service-reversed:hover .icon {
  background-color: var(--vtc-bg-main3);
  transition: all 0.4s;
}
.service1-single-slider.service-reversed:hover .icon img {
  filter: brightness(40);
  transition: all 0.4s;
}
.service1-single-slider.service-reversed:hover .number {
  transition: all 0.4s;
  padding-left: 0;
}
.service1-single-slider.service-reversed:hover .number::after {
  background-color: var(--vtc-text-sub-title-1);
  transition: all 0.4s;
  left: 30px;
}