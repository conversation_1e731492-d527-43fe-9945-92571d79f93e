<?php
// Get service from database
require_once __DIR__ . '/../admin/models/services_model.php';
require_once __DIR__ . '/../admin/models/service_accordion_model.php';
$serviceModel = new ServicesModel();
$accordionModel = new ServiceAccordionModel();

// Get service slug from URL
$segments = explode('/', $_GET['route'] ?? '');
$slug = $segments[1] ?? '';

// Get service by slug
$service = null;
if ($slug) {
    $service = $serviceModel->getServiceBySlug($slug);
}

// Get service accordions
$accordions = [];
if ($service) {
    $accordions = $accordionModel->getActiveAccordionsByService($service['id']);
}

// If service not found, use default content
if (!$service) {
    $service = [
        'id' => 0,
        'title_en' => 'ISO 9001 - Quality Management',
        'title_ro' => 'ISO 9001 - Managementul Calității',
        'content_en' => '<p><strong>The first edition of ISO 9001 was published in 1987</strong> and at that moment it was based on the requirements of a British standard (BS 5750).</p><p>From its first publication, ISO 9001 has been revised several times to adapt to the evolving business environment and to better meet the needs of organizations.</p>',
        'content_ro' => '<p><strong>Prima ediție a ISO 9001 a fost publicată în 1987</strong> și în acel moment se baza pe cerințele unui standard britanic (BS 5750).</p><p>De la prima sa publicare, ISO 9001 a fost revizuit de mai multe ori pentru a se adapta la mediul de afaceri în evoluție și pentru a răspunde mai bine nevoilor organizațiilor.</p>',
        'image' => 'assets/img/blog/blog-details-image1.jpg',
        'slug' => 'iso-9001-quality-management'
    ];
}

// Fetch 3 related services (excluding current)
$relatedServices = [];
try {
    $allServices = $serviceModel->getActiveServices();
    $relatedServices = array_filter($allServices, function($s) use ($service) {
        return $s['id'] != $service['id'];
    });
    $relatedServices = array_slice($relatedServices, 0, 3);
} catch (Exception $e) {
    // If there's an error, use empty array
    $relatedServices = [];
}
?>
<!--===== HERO AREA START ======-->
<div class="inner-hero bg-cover _relative">
	<div class="hero1-single-slider overflow-hidden _relative" style="background-image: url(<?= ASSETS_URL ?>/img/bg/hero1-bg.jpg);">
		<div class="container">
			<div class="row">
				<div class="col-lg-12">
					<div class="mt-40 inner-heading _relative z-9">
						<h1 class="text-56 md:text-40 sm:text-40 leading64 inline-block white1 font-semibold text-anime-style-3"><?= htmlspecialchars($service['title_' . LANG]) ?></h1>
						<div class="page-prog">
							<a href="<?= ROOT_URL ?>/"><?= t('nav_home', 'Home') ?></a>
							<p class="icon">
								<i class="fa-regular fa-angle-right"></i>
							</p>
							<a href="<?= ROOT_URL ?>/our-services"><?= t('nav_services', 'Services') ?></a>
							<p class="icon">
								<i class="fa-regular fa-angle-right"></i>
							</p>
							<p><?= htmlspecialchars($service['title_' . LANG]) ?></p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<!--===== HERO AREA END ======-->

<!--===== DETAILS AREA START ======-->
<div class="details-area pt-100 pb-100">
	<div class="container">
		<div class="row">
			<div class="col-lg-8">
				<div class="details-content">
					<article>
						<div class="details-content mt-30">
							<div class="heading mt-30">
								<h2 class="black1 pb-20 text-32 sm:text-30 md:text-30 leading-32 font-semibold"><?= htmlspecialchars($service['title_' . LANG]) ?></h2>
							</div>
							<div class="">
								<?php if ($service['image']): ?>
									<div class="image pb-3">
										<img src="<?= ROOT_URL ?>/<?= $service['image'] ?>" alt="<?= htmlspecialchars($service['title_' . LANG]) ?>" class="img-fluid">
									</div>
								<?php endif; ?>
								<?= $service['content_' . LANG] ?>
							</div>

						<!-- Service Accordions Section -->
						<?php if (!empty($accordions)): ?>
							<div class="accordion accordion1 mt-40" id="accordionExample">
								<?php foreach ($accordions as $index => $accordion):
									$title = LANG === 'ro' ? $accordion['title_ro'] : $accordion['title_en'];
									$content = LANG === 'ro' ? $accordion['content_ro'] : $accordion['content_en'];
									$collapseId = 'collapse' . $accordion['id'];
									$headingId = 'heading' . $accordion['id'];
								?>
									<div class="accordion-item">
										<h2 class="accordion-header" id="<?= $headingId ?>">
											<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#<?= $collapseId ?>" aria-expanded="false" aria-controls="<?= $collapseId ?>">
												<?= htmlspecialchars($title) ?>
											</button>
										</h2>
										<div id="<?= $collapseId ?>" class="accordion-collapse collapse" aria-labelledby="<?= $headingId ?>" data-bs-parent="#accordionExample">
											<div class="accordion-body">
												<?= $content ?>
											</div>
										</div>
									</div>
								<?php endforeach; ?>
							</div>
						<?php else: ?>
							<!-- Fallback content if no accordions are available -->
							<div class="accordion accordion1 mt-40" id="accordionExample">
								<div class="accordion-item">
									<h2 class="accordion-header" id="headingDefault">
										<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDefault" aria-expanded="false" aria-controls="collapseDefault">
											<?= t('service_default_accordion_title', 'Service Information') ?>
										</button>
									</h2>
									<div id="collapseDefault" class="accordion-collapse collapse" aria-labelledby="headingDefault" data-bs-parent="#accordionExample">
										<div class="accordion-body">
											<p><?= t('service_default_accordion_content', 'For detailed information about this service, please contact <NAME_EMAIL> or use the contact form on this page.') ?></p>
										</div>
									</div>
								</div>
							</div>
						<?php endif; ?>

					</article>
				</div>
			</div>
			<div class="col-lg-4">
				<div class="sidebar-area">
					<div class="_sidebar-widget _list mt-40">
						<h3><?= t('sidebar_download', 'Download') ?></h3>
						<div class="sidebar-list">
							<ul>
								<li>
									<a target="_blank" href="https://www.rigcert.org/upload_fisere/General_Rules_for_Management_System_certification.pdf"><?= t('sidebar_rules_certification', 'Rules for certification') ?>
										<span>
											<i class="fa-solid fa-angle-right"></i>
										</span>
									</a>
								</li>
								<li>
									<a target="_blank" href="https://www.rigcert.org/upload_fisere/RUCM_en.pdf"><?= t('sidebar_certification_mark', 'Use of certification mark') ?>
										<span>
											<i class="fa-solid fa-angle-right"></i>
										</span>
									</a>
								</li>
								<li>
									<a target="_blank" href="https://www.rigcert.org/upload_fisere/Rigcert_Brochure_2020.pdf"><?= t('sidebar_brochure', 'Our brochure') ?>
										<span>
											<i class="fa-solid fa-angle-right"></i>
										</span>
									</a>
								</li>
							</ul>
						</div>
					</div>
					<?php include __DIR__ . '/sections/sidebar-form.php'; ?>
				</div>
			</div>
		</div>

		<!-- Related Services Section -->
		<div class="heading mt-60">
			<h2 class="black1 pb-20 text-32 sm:text-30 md:text-30 leading-32 font-semibold"><?= t('related_services', 'Related Services') ?></h2>
		</div>

		<?php if (!empty($relatedServices)): ?>
			<div class="row _relative gx-2 mt-20" data-aos="fade-up" data-aos-duration="1000">
				<?php foreach ($relatedServices as $index => $relatedService): ?>
					<div class="m-0 col-md-4">
						<div class="service1-single-slider service-reversed h-100">
							<div class="icon">
								<?php if ($relatedService['image']): ?>
									<img src="<?= ROOT_URL ?>/<?= $relatedService['image'] ?>" alt="<?= htmlspecialchars($relatedService['title_' . LANG]) ?>">
								<?php else: ?>
									<img src="<?= ASSETS_URL ?>/img/icons/service1-icon<?= ($index % 3) + 1 ?>.svg" alt="">
								<?php endif; ?>
							</div>
							<a href="<?= ROOT_URL ?>/service-details/<?= $relatedService['slug'] ?>" class="arrow">
								<i class="fa-regular fa-arrow-right"></i>
							</a>
							<div class="heading1">
								<h4>
									<a href="<?= ROOT_URL ?>/service-details/<?= $relatedService['slug'] ?>" class="black1 font-24 text _hover1 leading-24 font-semibold"><?= htmlspecialchars($relatedService['title_' . LANG]) ?></a>
								</h4>
								<p class="text-18 leading-26 font-medium gray1 mt-16 mb-16"><?= htmlspecialchars(substr(strip_tags($relatedService['content_' . LANG]), 0, 120)) ?>...</p>
								<p class="number sub-text1 text-18 leading-26 font-medium"><?= str_pad($index + 1, 2, '0', STR_PAD_LEFT) ?></p>
							</div>
						</div>
					</div>
				<?php endforeach; ?>
			</div>
		<?php else: ?>
			<!-- Fallback related services if no database services available -->
			<div class="heading mt-60">
				<h2 class="black1 pb-20 text-32 sm:text-30 md:text-30 leading-32 font-semibold"><?= t('related_services', 'Related Services') ?></h2>
			</div>

			<div class="row _relative gx-2 mt-20" data-aos="fade-up" data-aos-duration="1000">
				<div class="m-0 col-md-4">
					<div class="service1-single-slider service-reversed h-100">
						<div class="icon">
							<img src="<?= ASSETS_URL ?>/img/icons/service1-icon1.svg" alt="">
						</div>
						<a href="<?= ROOT_URL ?>/service-details" class="arrow">
							<i class="fa-regular fa-arrow-right"></i>
						</a>
						<div class="heading1">
							<h4>
								<a href="<?= ROOT_URL ?>/service-details" class="black1 font-24 text _hover1 leading-24 font-semibold"><?= t('service_iso9001_title', 'ISO 9001 – Quality Management') ?></a>
							</h4>
							<p class="text-18 leading-26 font-medium gray1 mt-16 mb-16"><?= t('service_iso9001_description', 'Ensure consistent product and service quality, improve customer satisfaction, and streamline internal processes.') ?></p>
							<p class="number sub-text1 text-18 leading-26 font-medium">01</p>
						</div>
					</div>
				</div>
				<div class="m-0 col-md-4">
					<div class="service1-single-slider service-reversed h-100">
						<div class="icon">
							<img src="<?= ASSETS_URL ?>/img/icons/service1-icon2.svg" alt="">
						</div>
						<a href="<?= ROOT_URL ?>/service-details" class="arrow">
							<i class="fa-regular fa-arrow-right"></i>
						</a>
						<div class="heading1">
							<h4>
								<a href="<?= ROOT_URL ?>/service-details" class="black1 font-24 text _hover1 leading-24 font-semibold"><?= t('service_iso14001_title', 'ISO 14001 – Environmental Management') ?></a>
							</h4>
							<p class="text-18 leading-26 font-medium gray1 mt-16 mb-16"><?= t('service_iso14001_description', 'Demonstrate your commitment to sustainability by managing environmental impacts and complying with regulations.') ?></p>
							<p class="number sub-text1 text-18 leading-26 font-medium">02</p>
						</div>
					</div>
				</div>
				<div class="m-0 col-md-4">
					<div class="service1-single-slider service-reversed h-100">
						<div class="icon">
							<img src="<?= ASSETS_URL ?>/img/icons/service1-icon3.svg" alt="">
						</div>
						<a href="<?= ROOT_URL ?>/service-details" class="arrow">
							<i class="fa-regular fa-arrow-right"></i>
						</a>
						<div class="heading1">
							<h4>
								<a href="<?= ROOT_URL ?>/service-details" class="black1 font-24 text _hover1 leading-24 font-semibold"><?= t('service_iso45001_title', 'ISO 45001 – Occupational Health & Safety') ?></a>
							</h4>
							<p class="text-18 leading-26 font-medium gray1 mt-16 mb-16"><?= t('service_iso45001_description', 'Enhance workplace safety, reduce risks, and promote the well-being of employees through a proactive management system.') ?></p>
							<p class="number sub-text1 text-18 leading-26 font-medium">03</p>
						</div>
					</div>
				</div>
			</div>
		<?php endif; ?>

		<!-- FAQ Section -->
		<?php include __DIR__ . '/sections/faq-section.php'; ?>
	</div>
</div>
<!--===== DETAILS AREA END ======-->

<!-- Subscribe Section -->
<?php include __DIR__ . '/sections/subscribe-section.php'; ?>