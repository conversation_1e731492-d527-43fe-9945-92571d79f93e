<?php
// Get news from database
require_once __DIR__ . '/../admin/models/news_model.php';
$newsModel = new NewsModel();
$newsItems = $newsModel->getActiveNews();
?>
	<div class="inner-hero bg-cover _relative">
		<div class="hero1-single-slider overflow-hidden _relative" style="background-image: url(<?= ASSETS_URL ?>/img/bg/hero1-bg.jpg);">
			<div class="container">
				<div class="row align-items-center">
					<div class="col-lg-6">
						<div class="mt-40 inner-heading _relative z-9">
							<h1 class="text-56 md:text-40 sm:text-40 leading64 inline-block white1 font-semibold text-anime-style-3"><?= t('news_page_title', 'News') ?></h1>
							<div class="page-prog">
								<a href="<?= ROOT_URL ?>"><?= t('nav_home', 'Home') ?></a>
								<p class="icon">
									<i class="fa-regular fa-angle-right"></i>
								</p>
								<p><?= t('news_page_breadcrumb', 'News') ?></p>
							</div>
						</div>
					</div>
					<div class="col-lg-6">
						<div class="all-images _relative">
							<div class="circle-area">
								<div class="circle-arrow">
									<a href="#"><img src="<?= ASSETS_URL ?>/img/icons/hero1-circle-arrow.svg" alt="">
									</a>
								</div>
								<div class="circle-bg round-circle">
									<img src="<?= ASSETS_URL ?>/img/shapes/hero1-circle-bg.png" alt="">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="main-image">
				<img src="<?= ASSETS_URL ?>/img/hero/hero1-slider-image1.png" alt="">
			</div>
			<div class="main-image-shape">
				<img src="<?= ASSETS_URL ?>/img/shapes/hero1-shape.png" alt="">
			</div>
		</div>
	</div>
	<!--===== HERO AREA START ======-->
	<!--===== SERVICE AREA START ======-->
	<div class="service-page-sec1 sp">
		<div class="container">
			<div class="row">
				<?php if (empty($newsItems)): ?>
					<!-- Fallback content if no news in database -->
					<div class="col-lg-4 col-md-6" data-aos="zoom-in-up" data-aos-duration="1200">
						<div class="vl-blog-1-item white-bg2 mt-30">
							<div class="vl-blog-3-thumb overflow-hidden _relative">
								<img class="w-full" src="<?= ASSETS_URL ?>/img/blog/blog1-img1.png" alt="">
							</div>
							<div class="vl-blog-1-content p-28">
								<div class="vl-blog1-meta">
									<a href="#" class="date text-18 text _hover1 leading-18 inline-block black1 font-semibold">
										<img src="<?= ASSETS_URL ?>/img/icons/blog1-date.svg" alt=""> 8 December 2024
									</a>
								</div>
								<h4>
									<a href="<?= ROOT_URL ?>/blog-details" class="text-20 mt-12 text _hover1 leading-28 inline-block black1 font-semibold">ISO 9001 Set for Revision: What to Expect from the New Version</a>
								</h4>
								<a href="<?= ROOT_URL ?>/blog-details" class="learn1 text _hover1 font-18 uppercase mt-20 leading-18 inline-block black1 font-semibold"><?= t('news_learn_more', 'learn more') ?>
									<span class="arrow1">
										<i class="fa-regular fa-arrow-right"></i>
									</span>
									<span class="arrow2">
										<i class="fa-regular fa-arrow-right"></i>
									</span>
								</a>
							</div>
						</div>
					</div>
				<?php else: ?>
					<?php foreach ($newsItems as $news): ?>
						<div class="col-lg-4 col-md-6" data-aos="zoom-in-up" data-aos-duration="1200">
							<div class="vl-blog-1-item white-bg2 mt-30">
								<div class="vl-blog-3-thumb overflow-hidden _relative">
									<?php if ($news['image']): ?>
										<img class="w-full" src="<?= ROOT_URL ?>/<?= $news['image'] ?>" alt="<?= htmlspecialchars($news['title_' . LANG]) ?>">
									<?php else: ?>
										<img class="w-full" src="<?= ASSETS_URL ?>/img/blog/blog1-img1.png" alt="">
									<?php endif; ?>
								</div>
								<div class="vl-blog-1-content p-28">
									<div class="vl-blog1-meta">
										<a href="#" class="date text-18 text _hover1 leading-18 inline-block black1 font-semibold">
											<img src="<?= ASSETS_URL ?>/img/icons/blog1-date.svg" alt=""> <?= date('j F Y', strtotime($news['publish_date'])) ?>
										</a>
									</div>
									<h4>
										<a href="<?= ROOT_URL ?>/blog-details/<?= $news['slug'] ?>" class="text-20 mt-12 text _hover1 leading-28 inline-block black1 font-semibold"><?= htmlspecialchars($news['title_' . LANG]) ?></a>
									</h4>
									<a href="<?= ROOT_URL ?>/blog-details/<?= $news['slug'] ?>" class="learn1 text _hover1 font-18 uppercase mt-20 leading-18 inline-block black1 font-semibold"><?= t('news_learn_more', 'learn more') ?>
										<span class="arrow1">
											<i class="fa-regular fa-arrow-right"></i>
										</span>
										<span class="arrow2">
											<i class="fa-regular fa-arrow-right"></i>
										</span>
									</a>
								</div>
							</div>
						</div>
					<?php endforeach; ?>
				<?php endif; ?>
			</div>
			<div class="space50"></div>
		</div>
	</div>
	<!--===== SERVICE AREA END ======-->
	<?php include __DIR__ . '/sections/subscribe-section.php'; ?>