
	<!--===== HERO AREA START ======-->
	<div class="hero1 bg-cover _relative">
		<div class="hero1-sliders">
			<div class="hero1-single-slider overflow-hidden _relative" style="background-image: url(<?= ASSETS_URL ?>/img/bg/hero1-bg.jpg);">
				<div class="container">
					<div class="row align-items-center">
						<div class="col-lg-6">
							<div class="heading mt-lg-5 _relative z-9">
								<span class="sub-title text-18 leading-18 flex white1 uppercase mb-20 font-medium">
									<img src="<?= ASSETS_URL ?>/img/icons/span1.svg" alt="">  <?= t('hero_subtitle', 'BUILDING TRUST. ENABLING EXCELLENCE.') ?>
								</span>
								<h1 class="text-56 md:text-40 sm:text-40 leading64 inline-block white1 font-semibold text-anime-style-3"><?= t('hero_title', 'We are RIGCERT') ?></h1>
								<p class="mt-16 text-18 leading-26 inline-block white1"><?= t('hero_description', 'Your trusted partner for accredited certification services across Europe. We help organizations demonstrate compliance with international standards and build stakeholder confidence.') ?>
								</p>
								<div class="button mt-30">
									<a class="theme-btn1" href="<?= ROOT_URL ?>/contact">
										<span class="text"><?= t('request_quote', 'Request a quote') ?></span>
										<span class="arrow-all">
											<span class="arrow1">
												<i class="fa-regular fa-arrow-right"></i>
											</span>
											<span class="arrow2">
												<i class="fa-regular fa-arrow-right"></i>
											</span>
										</span>
									</a>
								</div>
							</div>
						</div>
						<div class="col-lg-6">
							<div class="all-images _relative">
								<div class="circle-area">
									<div class="circle-arrow">
										<a href="service.html">
											<img src="<?= ASSETS_URL ?>/img/icons/hero1-circle-arrow.svg" alt=""></a>
									</div>
									<div class="circle-bg round-circle">
										<img src="<?= ASSETS_URL ?>/img/shapes/hero1-circle-bg.png" alt="">
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="main-image">
					<img src="<?= ASSETS_URL ?>/img/hero/hero1-slider-image1.png" alt="">
				</div>
				<div class="main-image-shape">
					<img src="<?= ASSETS_URL ?>/img/shapes/hero1-shape.png" alt="">
				</div>
			</div>
		</div>
		<div class="hero1-arrow-buttons">
			<button class="tes2-prev-arrow">
				<i class="fa-solid fa-angle-left"></i>
			</button>
			<button class="tes2-next-arrow">
				<i class="fa-solid fa-angle-right"></i>
			</button>
		</div>
	</div>
	<!--===== HERO AREA START ======-->
	<?php include __DIR__ . '/sections/about-section.php'; ?>
	<!--===== SERVICE AREA START ======-->
	<div class="service1 sp sec-bg1 shape-bg1">
		<div class="container">
			<div class="row">
				<div class="col-lg-6">
					<div class="heading1 _relative z-3">
						<span data-aos="fade-left" data-aos-duration="900" class="sub-title1 sec-bg2 rounded-6 p-8-12 text-18 leading-18 inline-block sub-text1 uppercase mb-20 font-medium"><?= t('services_subtitle', 'Our Services') ?></span>
						<h2 class="black1 text-44 sm:text-30 md:text-30 leading-44 font-semibold text-anime-style-3"><?= t('services_title', 'Comprehensive Certification Solutions') ?></h2>
						<p data-aos="fade-right" data-aos-duration="800" class="mt-16 text-18 leading-26 font-medium gray1"><?= t('services_description', 'We provide a full range of certification services to help your organization achieve and maintain compliance with international standards.') ?></p>
						<div class="mt-30" data-aos="fade-right" data-aos-duration="1100">
							<a class="theme-btn1" href="<?= ROOT_URL ?>/contact">
								<span class="text"><?= t('request_quote', 'Request a Quote') ?></span>
								<span class="arrow-all">
									<span class="arrow1">
										<i class="fa-regular fa-arrow-right"></i>
									</span>
									<span class="arrow2">
										<i class="fa-regular fa-arrow-right"></i>
									</span>
								</span>
							</a>
						</div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="images _relative z-3 sm:mt-30 md:mt-30">
						<div class="image reveal _relative image-anime">
							<img src="<?= ASSETS_URL ?>/img/service/service1-image.png" class="rounded-20" alt="">
						</div>
						<div class="shape round-circle">
							<img src="<?= ASSETS_URL ?>/img/shapes/about1-shape3.png" alt="">
						</div>
					</div>
				</div>
			</div>
			<div class="row _relative z-3 mt-60" data-aos="fade-up" data-aos-duration="1000">
				<div class="service1-slider">
					<?php
					// Get services from database
					$services = getServices(6);

					// If no services found, use default content
					if (empty($services)) {
						// Default service items
						$defaultServices = [
							[
								'title_en' => 'ISO 9001 – Quality Management',
								'title_ro' => 'ISO 9001 – Managementul Calității',
								'short_description_en' => 'Ensure consistent product and service quality, improve customer satisfaction, and streamline internal processes.',
								'short_description_ro' => 'Asigurați calitatea constantă a produselor și serviciilor, îmbunătățiți satisfacția clienților și eficientizați procesele interne.',
								'icon' => 'img/icons/service1-icon1.svg',
								'slug' => 'iso-9001-quality-management'
							],
							[
								'title_en' => 'ISO 14001 – Environmental Management',
								'title_ro' => 'ISO 14001 – Managementul Mediului',
								'short_description_en' => 'Demonstrate your commitment to sustainability by managing environmental impacts and complying with regulations.',
								'short_description_ro' => 'Demonstrați angajamentul dvs. față de sustenabilitate prin gestionarea impacturilor asupra mediului și respectarea reglementărilor.',
								'icon' => 'img/icons/service1-icon2.svg',
								'slug' => 'iso-14001-environmental-management'
							],
							[
								'title_en' => 'ISO 45001 – Occupational Health and Safety',
								'title_ro' => 'ISO 45001 – Sănătate și Securitate Ocupațională',
								'short_description_en' => 'Protect your workforce, reduce accidents, and create a safer workplace through systematic risk management.',
								'short_description_ro' => 'Protejați forța de muncă, reduceți accidentele și creați un loc de muncă mai sigur prin gestionarea sistematică a riscurilor.',
								'icon' => 'img/icons/service1-icon3.svg',
								'slug' => 'iso-45001-occupational-health-safety'
							]
						];

						foreach ($defaultServices as $index => $service) {
							?>
							<div class="service1-single-slider">
								<div class="icon">
									<img src="<?= ASSETS_URL ?>/<?= $service['icon'] ?>" alt="">
								</div>
								<a href="<?= ROOT_URL ?>/service-details/<?= $service['slug'] ?>" class="arrow">
									<i class="fa-regular fa-arrow-right"></i>
								</a>
								<div class="heading1">
									<h4>
										<a href="<?= ROOT_URL ?>/service-details/<?= $service['slug'] ?>" class="black1 font-24 text _hover1 leading-24 font-semibold"><?= $service['title_' . LANG] ?></a>
									</h4>
									<p class="text-18 leading-26 font-medium gray1 mt-16 mb-16"><?= $service['short_description_' . LANG] ?></p>
									<p class="number sub-text1 text-18 leading-26 font-medium"><?= str_pad($index + 1, 2, '0', STR_PAD_LEFT) ?></p>
								</div>
							</div>
							<?php
						}
					} else {
						// Display services from database
						foreach ($services as $index => $service) {
							?>
							<div class="service1-single-slider">
								<div class="icon">
									<?php if ($service['icon']): ?>
										<img src="<?= ROOT_URL ?>/<?= $service['icon'] ?>" alt="<?= htmlspecialchars($service['title_' . LANG]) ?>">
									<?php else: ?>
										<img src="<?= ASSETS_URL ?>/img/icons/service1-icon<?= ($index % 6) + 1 ?>.svg" alt="">
									<?php endif; ?>
								</div>
								<a href="<?= ROOT_URL ?>/service-details/<?= $service['slug'] ?>" class="arrow">
									<i class="fa-regular fa-arrow-right"></i>
								</a>
								<div class="heading1">
									<h4>
										<a href="<?= ROOT_URL ?>/service-details/<?= $service['slug'] ?>" class="black1 font-24 text _hover1 leading-24 font-semibold"><?= htmlspecialchars($service['title_' . LANG]) ?></a>
									</h4>
									<p class="text-18 leading-26 font-medium gray1 mt-16 mb-16"><?= htmlspecialchars($service['short_description_' . LANG]) ?></p>
									<p class="number sub-text1 text-18 leading-26 font-medium"><?= str_pad($index + 1, 2, '0', STR_PAD_LEFT) ?></p>
								</div>
							</div>
							<?php
						}
					}
					?>
				</div>
			</div>
			<div class="row">
				<div class="col-lg-12">
					<div class="servie1-arrow-buttons text-end _relative z-3">
						<button class="service1-prev-arrow">
							<i class="fa-solid fa-angle-left"></i>
						</button>
						<button class="service1-next-arrow">
							<i class="fa-solid fa-angle-right"></i>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!--===== SERVICE AREA END ======-->
	<!--===== CASE STUDY START ======-->
	<div class="case1 sp" style="background-image: url(<?= ASSETS_URL ?>/img/bg/case1-bg.jpg);">
		<div class="container">
			<div class="row">
				<div class="col-lg-6 m-auto text-center">
					<div class="heading1-w">
						<span data-aos="fade-left" data-aos-duration="900" class="white1 sec-bg3 rounded-6 p-8-12 text-18 leading-18 inline-block uppercase mb-20 font-medium"><?= t('about_partners_subtitle', 'People who trust us') ?></span>
						<h2 class="text-44 white1 sm:text-30 md:text-30 leading-44 font-semibold text-anime-style-3"><?= t('about_partners_title', 'Our Partners') ?></h2>
					</div>
				</div>
			</div>
			<div class="space30"></div>

               <div class="about-brand-slider">
                  <div class="single-brand">
                     <img src="<?= ASSETS_URL ?>/img/logo/about-page-brand1.png" alt="">
                  </div>
                  <div class="single-brand">
                     <img src="<?= ASSETS_URL ?>/img/logo/about-page-brand2.png" alt="">
                  </div>
                  <div class="single-brand">
                     <img src="<?= ASSETS_URL ?>/img/logo/about-page-brand3.png" alt="">
                  </div>
                  <div class="single-brand">
                     <img src="<?= ASSETS_URL ?>/img/logo/about-page-brand4.png" alt="">
                  </div>
                  <div class="single-brand">
                     <img src="<?= ASSETS_URL ?>/img/logo/about-page-brand1.png" alt="">
                  </div>
                  <div class="single-brand">
                     <img src="<?= ASSETS_URL ?>/img/logo/about-page-brand2.png" alt="">
                  </div>
                  <div class="single-brand">
                     <img src="<?= ASSETS_URL ?>/img/logo/about-page-brand3.png" alt="">
                  </div>
                  <div class="single-brand">
                     <img src="<?= ASSETS_URL ?>/img/logo/about-page-brand4.png" alt="">
                  </div>
               </div>
		</div>
	</div>
	<!--===== CASE STUDY END ======-->
	<!--===== BLOG AREA START ======-->
	<div class="blog1 sp white-bg3">
		<div class="container">
			<div class="row">
				<div class="col-lg-9 m-auto text-center">
					<div class="heading1">
						<span data-aos="fade-left" data-aos-duration="900" class="sub-title1 sec-bg1 rounded-6 p-8-12 text-18 leading-18 inline-block sub-text1 uppercase mb-20 font-medium"><?= t('news_subtitle', 'Compliance & Certification News') ?></span>
						<h2 class="black1 text-44 sm:text-30 md:text-30 leading-44 font-semibold text-anime-style-3"><?= t('news_title', 'Explore the latest developments in standards, certification, and compliance') ?></h2>
					</div>
				</div>
			</div>
			<div class="row mt-30">
				<?php
				// Get recent news from database
				$recentNews = getRecentNews(3);

				// If no news found, use default content
				if (empty($recentNews)) {
					// Default news items
					$defaultNews = [
						[
							'title_en' => 'ISO 9001 Set for Revision: What to Expect from the New Version',
							'title_ro' => 'ISO 9001 în curs de revizuire: Ce să așteptați de la noua versiune',
							'publish_date' => date('Y-m-d'),
							'image' => 'img/blog/blog1-img1.png',
							'slug' => 'iso-9001-revision'
						],
						[
							'title_en' => 'Cybersecurity in Focus: ISO/IEC 27001 Gaining Global Momentum',
							'title_ro' => 'Securitatea cibernetică în centrul atenției: ISO/IEC 27001 câștigă impuls global',
							'publish_date' => date('Y-m-d'),
							'image' => 'img/blog/blog1-img2.png',
							'slug' => 'cybersecurity-iso-27001'
						],
						[
							'title_en' => 'ESG and ISO Standards: The Growing Intersection',
							'title_ro' => 'ESG și standardele ISO: Intersecția în creștere',
							'publish_date' => date('Y-m-d'),
							'image' => 'img/blog/blog1-img3.png',
							'slug' => 'esg-iso-standards'
						]
					];

					foreach ($defaultNews as $index => $news) {
						$duration = 1200 - ($index * 200);
						?>
						<div class="col-lg-4 col-md-6" data-aos="zoom-in-up" data-aos-duration="<?= $duration ?>">
							<div class="vl-blog-1-item white-bg2 mt-30">
								<div class="vl-blog-3-thumb overflow-hidden _relative">
									<img class="w-full" src="<?= ASSETS_URL ?>/<?= $news['image'] ?>" alt="">
								</div>
								<div class="vl-blog-1-content p-28">
									<div class="vl-blog1-meta">
										<a href="#" class="date text-18 text _hover1 leading-18 inline-block black1 font-semibold">
											<img src="<?= ASSETS_URL ?>/img/icons/blog1-date.svg" alt=""> <?= date('j F Y', strtotime($news['publish_date'])) ?>
										</a>
									</div>
									<h4>
										<a href="<?= ROOT_URL ?>/blog-details/<?= $news['slug'] ?>" class="text-20 mt-12 text _hover1 leading-28 inline-block black1 font-semibold"><?= $news['title_' . LANG] ?></a>
									</h4>
									<a href="<?= ROOT_URL ?>/blog-details/<?= $news['slug'] ?>" class="learn1 text _hover1 font-18 uppercase mt-20 leading-18 inline-block black1 font-semibold"><?= t('news_learn_more', 'learn more') ?>
										<span class="arrow1">
											<i class="fa-regular fa-arrow-right"></i>
										</span>
										<span class="arrow2">
											<i class="fa-regular fa-arrow-right"></i>
										</span>
									</a>
								</div>
							</div>
						</div>
						<?php
					}
				} else {
					// Display news from database
					foreach ($recentNews as $index => $news) {
						$duration = 1200 - ($index * 200);
						?>
						<div class="col-lg-4 col-md-6" data-aos="zoom-in-up" data-aos-duration="<?= $duration ?>">
							<div class="vl-blog-1-item white-bg2 mt-30">
								<div class="vl-blog-3-thumb overflow-hidden _relative">
									<?php if ($news['image']): ?>
										<img class="w-full" src="<?= ROOT_URL ?>/<?= $news['image'] ?>" alt="<?= htmlspecialchars($news['title_' . LANG]) ?>">
									<?php else: ?>
										<img class="w-full" src="<?= ASSETS_URL ?>/img/blog/blog1-img<?= ($index % 3) + 1 ?>.png" alt="">
									<?php endif; ?>
								</div>
								<div class="vl-blog-1-content p-28">
									<div class="vl-blog1-meta">
										<a href="#" class="date text-18 text _hover1 leading-18 inline-block black1 font-semibold">
											<img src="<?= ASSETS_URL ?>/img/icons/blog1-date.svg" alt=""> <?= date('j F Y', strtotime($news['publish_date'])) ?>
										</a>
									</div>
									<h4>
										<a href="<?= ROOT_URL ?>/blog-details/<?= $news['slug'] ?>" class="text-20 mt-12 text _hover1 leading-28 inline-block black1 font-semibold"><?= htmlspecialchars($news['title_' . LANG]) ?></a>
									</h4>
									<a href="<?= ROOT_URL ?>/blog-details/<?= $news['slug'] ?>" class="learn1 text _hover1 font-18 uppercase mt-20 leading-18 inline-block black1 font-semibold"><?= t('news_learn_more', 'learn more') ?>
										<span class="arrow1">
											<i class="fa-regular fa-arrow-right"></i>
										</span>
										<span class="arrow2">
											<i class="fa-regular fa-arrow-right"></i>
										</span>
									</a>
								</div>
							</div>
						</div>
						<?php
					}
				}
				?>
			</div>
		</div>
	</div>
	<!--===== BLOG AREA END ======-->
	<?php include __DIR__ . '/sections/subscribe-section.php'; ?>