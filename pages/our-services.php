<?php
// Get services from database
require_once __DIR__ . '/../admin/models/services_model.php';
$servicesModel = new ServicesModel();
$services = $servicesModel->getActiveServices();
?>
<div class="inner-hero bg-cover _relative">
	<div class="hero1-single-slider overflow-hidden _relative" style="background-image: url(<?= ASSETS_URL ?>/img/bg/hero1-bg.jpg);">
		<div class="container">
			<div class="row align-items-center">
				<div class="col-lg-6">
					<div class="mt-40 inner-heading _relative z-9">
						<h1 class="text-46 pt-5 md:text-40 sm:text-40 leading64 inline-block white1 font-semibold text-anime-style-3"><?= t('services_page_title', 'Management Systems Certification') ?></h1>
						<div class="page-prog">
							<a class="fs-6" href="<?= ROOT_URL ?>"><?= t('nav_home', 'Home') ?></a>
							<p class="icon">
								<i class="fa-regular fa-angle-right"></i>
							</p>
							<p class="fs-6"><?= t('services_page_breadcrumb', 'Services') ?></p>
						</div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="all-images _relative">
						<div class="circle-area">
							<div class="circle-arrow">
								<a href="#"><img src="<?= ASSETS_URL ?>/img/icons/hero1-circle-arrow.svg" alt="">
								</a>
							</div>
							<div class="circle-bg round-circle">
								<img src="<?= ASSETS_URL ?>/img/shapes/hero1-circle-bg.png" alt="">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="main-image">
			<img src="<?= ASSETS_URL ?>/img/hero/hero1-slider-image1.png" alt="">
		</div>
		<div class="main-image-shape">
			<img src="<?= ASSETS_URL ?>/img/shapes/hero1-shape.png" alt="">
		</div>
	</div>
</div>
<!--===== HERO AREA START ======-->
<!--===== SERVICE AREA START ======-->
<div class="service-page-sec1 sp">
	<div class="container">
		<div class="row">
			<?php if (empty($services)): ?>
				<!-- Fallback content if no services in database -->
				<div class="col-lg-4 col-md-6 mt-30">
					<div class="service-page-item">
						<div class="icon">
							<img src="<?= ASSETS_URL ?>/img/icons/service1-icon1.svg" alt="">
						</div>
						<a href="<?= ROOT_URL ?>/service-details" class="arrow">
							<i class="fa-regular fa-arrow-right"></i>
						</a>
						<div class="heading1">
							<h4>
								<a href="<?= ROOT_URL ?>/service-details" class="black1 font-24 text _hover1 leading-24 font-semibold"><?= t('service_iso9001_title', 'ISO 9001 – Quality Management') ?></a>
							</h4>
							<p class="text-18 leading-26 font-medium gray1 mt-16 mb-16"><?= t('service_iso9001_description', 'Ensure consistent product and service quality, improve customer satisfaction, and streamline internal processes.') ?></p>
							<p class="number sub-text1 text-18 leading-26 font-medium">01</p>
						</div>
					</div>
				</div>
			<?php else: ?>
				<?php foreach ($services as $index => $service): ?>
					<div class="col-lg-4 col-md-6 mt-30">
						<div class="service-page-item">
							<div class="icon">
								<?php if ($service['icon']): ?>
									<img src="<?= ROOT_URL ?>/<?= $service['icon'] ?>" alt="<?= htmlspecialchars($service['title_' . LANG]) ?>">
								<?php else: ?>
									<img src="<?= ASSETS_URL ?>/img/icons/service1-icon<?= ($index % 6) + 1 ?>.svg" alt="">
								<?php endif; ?>
							</div>
							<a href="<?= ROOT_URL ?>/service-details/<?= $service['slug'] ?>" class="arrow">
								<i class="fa-regular fa-arrow-right"></i>
							</a>
							<div class="heading1">
								<h4>
									<a href="<?= ROOT_URL ?>/service-details/<?= $service['slug'] ?>" class="black1 font-24 text _hover1 leading-24 font-semibold"><?= htmlspecialchars($service['title_' . LANG]) ?></a>
								</h4>
								<p class="text-18 leading-26 font-medium gray1 mt-16 mb-16"><?= htmlspecialchars($service['short_description_' . LANG]) ?></p>
								<p class="number sub-text1 text-18 leading-26 font-medium"><?= str_pad($index + 1, 2, '0', STR_PAD_LEFT) ?></p>
							</div>
						</div>
					</div>
				<?php endforeach; ?>
			<?php endif; ?>
		</div>
		<div class="space50"></div>
	</div>
</div>
<!--===== SERVICE AREA END ======-->
<?php include __DIR__ . '/sections/subscribe-section.php'; ?>