<article>
	<div class="details-content mt-50">
		<div class="heading mt-30">
			<h2 class="black1 pb-20 text-32 sm:text-30 md:text-30 leading-32 font-semibold"><?= t('faq_title', 'Frequently Asked Question (FAQ)') ?></h2>
		</div>

		<?php
		// Get FAQ items from database
		try {
			$faqItems = query("SELECT * FROM faq WHERE active = 1 ORDER BY order_number ASC, id ASC")->fetchAll();
		} catch (Exception $e) {
			$faqItems = [];
		}

		if (!empty($faqItems)):
		?>
			<div class="accordion accordion1" id="accordionExample">
				<div class="row gx-3">
					<?php
					$totalItems = count($faqItems);
					$itemsPerColumn = ceil($totalItems / 2);
					$currentColumn = 1;
					$itemCount = 0;

					foreach ($faqItems as $index => $faq):
						$question = LANG === 'ro' ? $faq['question_ro'] : $faq['question_en'];
						$answer = LANG === 'ro' ? $faq['answer_ro'] : $faq['answer_en'];
						$collapseId = 'collapse' . $faq['id'];
						$headingId = 'heading' . $faq['id'];

						// Start new column if needed
						if ($itemCount == 0) {
							echo '<div class="col-lg-6">';
						}
					?>
						<div class="accordion-item">
							<h2 class="accordion-header" id="<?= $headingId ?>">
								<button class="accordion-button collapsed fs-6" type="button" data-bs-toggle="collapse" data-bs-target="#<?= $collapseId ?>" aria-expanded="false" aria-controls="<?= $collapseId ?>">
									<?= htmlspecialchars($question) ?>
								</button>
							</h2>
							<div id="<?= $collapseId ?>" class="accordion-collapse collapse" aria-labelledby="<?= $headingId ?>" data-bs-parent="#accordionExample">
								<div class="accordion-body">
									<?= $answer ?>
								</div>
							</div>
						</div>
					<?php
						$itemCount++;

						// Close column if we've reached the items per column or it's the last item
						if ($itemCount >= $itemsPerColumn || $index == $totalItems - 1) {
							echo '</div>';
							$itemCount = 0;
							$currentColumn++;
						}
					endforeach;
					?>
				</div>
			</div>
		<?php else: ?>
			<!-- Fallback FAQ content if no database content is available -->
			<div class="accordion accordion1" id="accordionExample">
				<div class="row gx-3">
					<div class="col-lg-6">
						<div class="accordion-item">
							<h2 class="accordion-header" id="heading1">
								<button class="accordion-button collapsed fs-6" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1" aria-expanded="false" aria-controls="collapse1">
									<?= t('faq_q1', 'What exactly is a management system?') ?>
								</button>
							</h2>
							<div id="collapse1" class="accordion-collapse collapse" aria-labelledby="heading1" data-bs-parent="#accordionExample">
								<div class="accordion-body">
									<?= t('faq_a1', 'A management system is a set of policies, processes and procedures used by an organization to ensure that it can fulfill the tasks required to achieve its objectives. These objectives cover many aspects of the organization\'s operations (such as meeting customer requirements, complying with regulations, meeting environmental objectives, meeting occupational health and safety objectives, etc.).') ?>
								</div>
							</div>
						</div>
					</div>
					</div>
				</div>
			<?php endif; ?>
		</div>
	</div>
</article>